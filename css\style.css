/* Fullscreen Loader Styles */
.loader-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10000;
  transition: opacity 0.5s ease;
  overflow: hidden;
}
.loader-container::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
  }
  50% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
  }
  100% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
  }
}

.loader-content {
  text-align: center;
  color: white;
  position: relative;
  z-index: 2;
}

/* Floating particles */
.particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 1;
}

.particle {
  position: absolute;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  animation: float 6s ease-in-out infinite;
}

.particle:nth-child(1) {
  width: 20px;
  height: 20px;
  left: 10%;
  animation-delay: 0s;
  animation-duration: 6s;
}

.particle:nth-child(2) {
  width: 15px;
  height: 15px;
  left: 20%;
  animation-delay: 1s;
  animation-duration: 8s;
}

.particle:nth-child(3) {
  width: 25px;
  height: 25px;
  left: 30%;
  animation-delay: 2s;
  animation-duration: 7s;
}

.particle:nth-child(4) {
  width: 18px;
  height: 18px;
  left: 50%;
  animation-delay: 3s;
  animation-duration: 9s;
}

.particle:nth-child(5) {
  width: 22px;
  height: 22px;
  left: 70%;
  animation-delay: 1.5s;
  animation-duration: 6.5s;
}

.particle:nth-child(6) {
  width: 16px;
  height: 16px;
  left: 80%;
  animation-delay: 2.5s;
  animation-duration: 8.5s;
}

.particle:nth-child(7) {
  width: 24px;
  height: 24px;
  left: 90%;
  animation-delay: 0.5s;
  animation-duration: 7.5s;
}

.particle:nth-child(8) {
  width: 19px;
  height: 19px;
  left: 60%;
  animation-delay: 3.5s;
  animation-duration: 6.8s;
}

@keyframes float {
  0% {
    transform: translateY(100vh) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100px) rotate(360deg);
    opacity: 0;
  }
}

.loader-animation {
  margin-bottom: 30px;
}

.wallet-icon {
  font-size: 4rem;
  margin-bottom: 20px;
  position: relative;
  animation: walletPulse 2s ease-in-out infinite;
}

.wallet-icon::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 80px;
  height: 80px;
  border: 3px solid transparent;
  border-top: 3px solid rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: spin 1.5s linear infinite;
}

.wallet-icon i {
  color: #fff;
  text-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
  position: relative;
  z-index: 1;
}

@keyframes spin {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

@keyframes walletPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

.loading-dots {
  display: flex;
  justify-content: center;
  gap: 8px;
  margin-bottom: 20px;
}

.dot {
  width: 12px;
  height: 12px;
  background: white;
  border-radius: 50%;
  animation: dotBounce 1.4s ease-in-out infinite both;
}

.dot:nth-child(1) { animation-delay: -0.32s; }
.dot:nth-child(2) { animation-delay: -0.16s; }
.dot:nth-child(3) { animation-delay: 0s; }

@keyframes dotBounce {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.loader-text h2 {
  font-family: "Kalpurush", "Noto Sans Bengali", sans-serif;
  font-size: 2.5rem;
  margin-bottom: 10px;
  font-weight: 600;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  animation: titleGlow 2s ease-in-out infinite alternate;
}

@keyframes titleGlow {
  0% {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3), 0 0 10px rgba(255, 255, 255, 0.3);
  }
  100% {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3), 0 0 20px rgba(255, 255, 255, 0.6);
  }
}

/* Developer Name Animation */
.developer-name {
  font-family: "Poppins", sans-serif;
  font-size: 2.2rem;
  font-weight: 700;
  margin-bottom: 20px;
  color: #ffffff;
  text-shadow:
    0 2px 4px rgba(0, 0, 0, 0.5),
    0 0 20px rgba(255, 255, 255, 0.8),
    0 0 30px rgba(255, 255, 255, 0.6),
    0 0 40px rgba(255, 255, 255, 0.4);
  text-align: center;
  letter-spacing: 2px;
  text-transform: uppercase;
  animation: nameGlow 2s ease-in-out infinite alternate, namePulse 3s ease-in-out infinite;
  border: 2px solid rgba(255, 255, 255, 0.3);
  padding: 15px 30px;
  border-radius: 50px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

@keyframes nameGlow {
  0% {
    text-shadow:
      0 2px 4px rgba(0, 0, 0, 0.5),
      0 0 20px rgba(255, 255, 255, 0.8),
      0 0 30px rgba(255, 255, 255, 0.6),
      0 0 40px rgba(255, 255, 255, 0.4);
  }
  100% {
    text-shadow:
      0 2px 4px rgba(0, 0, 0, 0.5),
      0 0 30px rgba(255, 255, 255, 1),
      0 0 40px rgba(255, 255, 255, 0.8),
      0 0 50px rgba(255, 255, 255, 0.6);
  }
}

@keyframes namePulse {
  0%, 100% {
    transform: scale(1);
    border-color: rgba(255, 255, 255, 0.3);
    background: rgba(255, 255, 255, 0.1);
  }
  50% {
    transform: scale(1.05);
    border-color: rgba(255, 255, 255, 0.6);
    background: rgba(255, 255, 255, 0.2);
  }
}



.loader-text p {
  font-family: "Kalpurush", "Noto Sans Bengali", sans-serif;
  font-size: 1.2rem;
  margin-bottom: 30px;
  opacity: 0.9;
}

.progress-bar {
  width: 300px;
  height: 6px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
  overflow: hidden;
  margin: 0 auto 15px;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #fff, #f0f0f0);
  border-radius: 3px;
  transition: width 0.3s ease;
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

.loading-percentage {
  font-family: "Poppins", sans-serif;
  font-size: 1rem;
  font-weight: 500;
  opacity: 0.9;
}

/* Responsive Design for Loader */
@media (max-width: 768px) {
  .wallet-icon {
    font-size: 3rem;
  }

  .wallet-icon::before {
    width: 60px;
    height: 60px;
  }

  .loader-text h2 {
    font-size: 2rem;
  }

  .developer-name {
    font-size: 1.8rem;
    padding: 12px 25px;
    letter-spacing: 1px;
  }

  .loader-text p {
    font-size: 1rem;
  }

  .progress-bar {
    width: 250px;
  }
}

@media (max-width: 480px) {
  .wallet-icon {
    font-size: 2.5rem;
  }

  .wallet-icon::before {
    width: 50px;
    height: 50px;
  }

  .loader-text h2 {
    font-size: 1.8rem;
  }

  .developer-name {
    font-size: 1.4rem;
    padding: 10px 20px;
    letter-spacing: 0.5px;
  }

  .progress-bar {
    width: 200px;
  }
}

/* CSS Variables for Theme */
:root {
  --primary-color: #3498db;
  --primary-color-rgb: 52, 152, 219;
  --secondary-color: #2ecc71;
  --danger-color: #e74c3c;
  --warning-color: #f39c12;
  --info-color: #17a2b8;
  --dark-color: #2c3e50;
  --light-color: #ecf0f1;
  --bg-color: #ffffff;
  --text-color: #2c3e50;
  --border-color: #bdc3c7;
  --shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  --border-radius: 8px;
  --transition: all 0.3s ease;

  /* Font Variables */
  --bengali-font: "Kalpurush", "Noto Sans Bengali", sans-serif;
  --number-font: "Noto Sans Bengali", "Kalpurush", sans-serif;
  --english-font: "Poppins", sans-serif;
}

/* Font Classes for Different Content Types */
.bengali-text {
  font-family: var(--bengali-font) !important;
  font-weight: 400 !important;
}

.number-text, .amount, .balance, .stat-amount, .balance-amount,
.transaction-amount, .search-result-amount, .notification-badge,
.word-count, .char-count, .loan-amount, .bank-amount,
[class*="amount"], [class*="balance"], [class*="total"],
.currency, .price, .value, .count, .number {
  font-family: var(--number-font) !important;
  font-weight: 400 !important;
}

.english-text {
  font-family: var(--english-font) !important;
  font-weight: 400 !important;
}

/* Global Font Weight Override (excluding icons and their containers) */
body, h1, h2, h3, h4, h5, h6, p, span:not([class*="fa"]):not(.icon),
label, input, textarea, select,
.transaction-info h4, .transaction-info p,
.notification-item-title, .notification-item-content,
.search-result-title, .search-result-description,
.form-group label, .stat-info h3, .chart-header h3 {
  font-weight: 400 !important;
}

/* Button and Nav Tab text only (not icons) */
.btn:not(.icon):not([class*="fa"]),
.nav-tab:not(.icon):not([class*="fa"]) {
  font-weight: 400 !important;
}

/* Button and Nav Tab text content */
.btn span:not([class*="fa"]):not(.icon),
.nav-tab span:not([class*="fa"]):not(.icon) {
  font-weight: 400 !important;
}

/* Preserve ALL icon fonts and weights */
i, .fa, .fas, .far, .fab, .fal, .fad, .fak,
[class^="fa-"], [class*=" fa-"], .icon, [class*="icon-"] {
  font-weight: 900 !important; /* Font Awesome needs 900 for solid icons */
  font-family: "Font Awesome 6 Free", "Font Awesome 6 Pro", "Font Awesome 6 Brands" !important;
}

/* Specific Font Awesome weights */
.fas, .fa-solid {
  font-weight: 900 !important;
}

.far, .fa-regular {
  font-weight: 400 !important;
}

.fab, .fa-brands {
  font-weight: 400 !important;
}

.fal, .fa-light {
  font-weight: 300 !important;
}

/* Override any font-weight changes for buttons and elements containing icons */
button i, .btn i, .nav-tab i, .notification-item i,
.search-result i, .transaction-item i, .stat-card i,
.chart-header i, .form-group i, span i, div i, a i {
  font-weight: 900 !important;
}

/* Ensure icon containers don't override icon fonts */
.icon-container, .btn-icon, .nav-icon, .stat-icon {
  font-weight: inherit !important;
}

.icon-container i, .btn-icon i, .nav-icon i, .stat-icon i {
  font-weight: 900 !important;
}

/* CRITICAL: Force all Font Awesome icons to display properly */
i[class*="fa"], [class*="fa-"], .fa, .fas, .far, .fab, .fal, .fad, .fak {
  font-weight: 900 !important;
  font-family: "Font Awesome 6 Free" !important;
  font-style: normal !important;
}

/* Specific Font Awesome weights - MUST be preserved */
.far, .fa-regular, i.far, i.fa-regular {
  font-weight: 400 !important;
  font-family: "Font Awesome 6 Free" !important;
}

.fab, .fa-brands, i.fab, i.fa-brands {
  font-weight: 400 !important;
  font-family: "Font Awesome 6 Brands" !important;
}

.fal, .fa-light, i.fal, i.fa-light {
  font-weight: 300 !important;
  font-family: "Font Awesome 6 Pro" !important;
}

/* Ensure icons in any container maintain proper font */
button i, .btn i, .nav-tab i, span i, div i, a i,
.notification-item i, .search-result i, .transaction-item i,
.stat-card i, .chart-header i, .form-group i {
  font-family: "Font Awesome 6 Free" !important;
  font-weight: 900 !important;
  font-style: normal !important;
}

/* Global Font Weight Override */
* {
  font-weight: 400 !important;
}

/* Dark Theme */
[data-theme="dark"] {
  --bg-color: #1a1a1a;
  --text-color: #ffffff;
  --border-color: #444444;
  --shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  --dark-color: #ffffff;
  --light-color: #2c2c2c;
}

/* Dark Mode Balance Highlight */
[data-theme="dark"] .current-balance {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);
  border: 2px solid rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .current-balance:hover {
  box-shadow: 0 12px 35px rgba(255, 107, 107, 0.4);
}

[data-theme="dark"] .balance-amount {
  color: #ffffff;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
}

[data-theme="dark"] .balance-amount {
  animation: balanceGlowDark 2s ease-in-out infinite alternate;
}

[data-theme="dark"] .current-balance i {
  color: #ffd700;
}

/* Dark Mode Income Statistics */
[data-theme="dark"] .income-stat-card {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .income-stat-card.total-income {
  background: linear-gradient(135deg, #1e3a2e 0%, #2d5a27 100%);
  box-shadow: 0 8px 25px rgba(46, 204, 113, 0.2);
}

[data-theme="dark"] .income-stat-card.monthly-income {
  background: linear-gradient(135deg, #1e2a3a 0%, #2c3e50 100%);
  box-shadow: 0 8px 25px rgba(52, 152, 219, 0.2);
}

[data-theme="dark"] .income-stat-card.average-income {
  background: linear-gradient(135deg, #3a2e1e 0%, #5d4e37 100%);
  box-shadow: 0 8px 25px rgba(243, 156, 18, 0.2);
}

[data-theme="dark"] .income-stat-card.last-income {
  background: linear-gradient(135deg, #3a2e1e 0%, #5d4e37 100%);
  box-shadow: 0 8px 25px rgba(230, 126, 34, 0.2);
}

[data-theme="dark"] .income-stat-card:hover {
  box-shadow: 0 15px 40px rgba(255, 255, 255, 0.1);
}

/* Dark Mode Expense Statistics */
[data-theme="dark"] .expense-stat-card {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .expense-stat-card.total-expense {
  background: linear-gradient(135deg, #3a1e1e 0%, #5d2727 100%);
  box-shadow: 0 8px 25px rgba(231, 76, 60, 0.2);
}

[data-theme="dark"] .expense-stat-card.monthly-expense {
  background: linear-gradient(135deg, #3a2e1e 0%, #5d4e37 100%);
  box-shadow: 0 8px 25px rgba(230, 126, 34, 0.2);
}

[data-theme="dark"] .expense-stat-card.average-expense {
  background: linear-gradient(135deg, #3a2e1e 0%, #5d4e37 100%);
  box-shadow: 0 8px 25px rgba(243, 156, 18, 0.2);
}

[data-theme="dark"] .expense-stat-card.last-expense {
  background: linear-gradient(135deg, #3a2e1e 0%, #5d4e37 100%);
  box-shadow: 0 8px 25px rgba(230, 126, 34, 0.2);
}

[data-theme="dark"] .expense-stat-card:hover {
  box-shadow: 0 15px 40px rgba(255, 255, 255, 0.1);
}

/* Dark Mode Loan Statistics */
[data-theme="dark"] .loan-stat-card {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .loan-stat-card.total-loan-taken {
  background: linear-gradient(135deg, #3a1e1e 0%, #5d2727 100%);
  box-shadow: 0 8px 25px rgba(231, 76, 60, 0.2);
}

[data-theme="dark"] .loan-stat-card.total-loan-paid {
  background: linear-gradient(135deg, #1e3a2e 0%, #2d5a27 100%);
  box-shadow: 0 8px 25px rgba(39, 174, 96, 0.2);
}

[data-theme="dark"] .loan-stat-card.total-loan-given {
  background: linear-gradient(135deg, #1e2d3a 0%, #2d4a5d 100%);
  box-shadow: 0 8px 25px rgba(52, 152, 219, 0.2);
}

[data-theme="dark"] .loan-stat-card.total-loan-returned {
  background: linear-gradient(135deg, #2d1e3a 0%, #4a2d5d 100%);
  box-shadow: 0 8px 25px rgba(155, 89, 182, 0.2);
}

[data-theme="dark"] .loan-stat-card:hover {
  box-shadow: 0 15px 40px rgba(255, 255, 255, 0.1);
}

/* Dark Mode Bank Statistics */
[data-theme="dark"] .bank-stat-card {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .bank-stat-card.current-balance {
  background: linear-gradient(135deg, #2d3561 0%, #3d4574 100%);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.2);
}

[data-theme="dark"] .bank-stat-card.last-transaction {
  background: linear-gradient(135deg, #5d2d4f 0%, #6d3d5f 100%);
  box-shadow: 0 8px 25px rgba(240, 147, 251, 0.2);
}

[data-theme="dark"] .bank-stat-card.total-deposit {
  background: linear-gradient(135deg, #1e3a2e 0%, #2d5a27 100%);
  box-shadow: 0 8px 25px rgba(39, 174, 96, 0.2);
}

[data-theme="dark"] .bank-stat-card.total-withdraw {
  background: linear-gradient(135deg, #3a2e1e 0%, #5d4e37 100%);
  box-shadow: 0 8px 25px rgba(230, 126, 34, 0.2);
}

[data-theme="dark"] .bank-stat-card:hover {
  box-shadow: 0 15px 40px rgba(255, 255, 255, 0.1);
}

@keyframes balanceGlowDark {
  0% {
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.5),
      0 0 20px rgba(255, 255, 255, 0.3);
  }
  100% {
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.5),
      0 0 30px rgba(255, 255, 255, 0.6);
  }
}

/* Global Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: var(--bengali-font);
  background-color: var(--bg-color);
  color: var(--text-color);
  line-height: 1.6;
  transition: var(--transition);
  direction: ltr;
  text-align: left;
  font-size: 18px; /* Increased from default 16px */
}
.developer-website{
    text-decoration: none;
    color: #fff;
    font-size: 20px;
    font-weight: 600;
    letter-spacing: 2px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5), 0 0;
    transition: all 0.3s ease;
}
.container {
  max-width: 1600px;
  margin: 0 auto;
  padding: 30px;
  min-height: 100vh;
  position: relative;
}
.custom_fs_btn{
  width: 36px !important;
  height: 36px !important;
}
/* Header Styles */
.header {
  background: linear-gradient(
    135deg,
    var(--header-primary, var(--primary-color)),
    var(--header-secondary, var(--secondary-color))
  );
  color: white;
  height: 100px; /* Increased height for better appearance */
  padding: 0 25px; /* Increased padding */
  border-radius: 12px; /* Larger border radius */
  margin-bottom: 20px; /* Increased margin */
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15),
              0 0 30px rgba(52, 152, 219, 0.3); /* Enhanced shadow with glow */
  animation: slideInDown 0.5s ease, headerGlow 3s ease-in-out infinite;
  position: sticky;
  top: 0;
  z-index: 1000;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  display: flex;
  align-items: center; /* Center content vertically */
  border: 1px solid rgba(255, 255, 255, 0.1); /* Subtle border */
}

/* Header scroll effect */
.header.scrolled {
  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.2),
              0 0 40px rgba(52, 152, 219, 0.4);
  backdrop-filter: blur(15px);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 100%; /* Full height */
  gap: 20px; /* Increased gap */
}

.header-left {
  flex: 0 0 auto; /* Don't grow or shrink */
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 5px;
}

.header-center {
  flex: 1; /* Take remaining space */
  display: flex;
  justify-content: center;
  align-items: center;
}

.header-controls {
  flex: 0 0 auto; /* Don't grow or shrink */
  display: flex;
  align-items: center;
  gap: 12px; /* Increased gap between controls */
}

.header h1 {
  font-size: 1.7rem; /* Larger font for better visibility */
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 10px; /* Increased gap */
  color: white;
  margin: 0;
  line-height: 1;
  white-space: nowrap; /* Prevent wrapping */
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3); /* Text shadow for better readability */
}

.current-datetime {
  color: white;
  font-size: 0.75rem; /* Smaller font size */
  opacity: 0.8;
  display: flex;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3); /* Text shadow for better readability */
  margin-top: 2px;
  align-items: center;
  gap: 8px; /* Smaller gap */
  font-weight: 400;
  white-space: nowrap; /* Prevent wrapping */
}

.datetime-item {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 4px 8px;
  background: rgba(52, 152, 219, 0.1);
  border-radius: 4px;
  transition: var(--transition);
}

.datetime-item:hover {
  background: rgba(52, 152, 219, 0.15);
}

.datetime-item i {
  color: var(--primary-color);
  font-size: 0.8rem;
}

.datetime-item span {
  font-family: var(--bengali-font);
  font-weight: 400;
  font-size: 1rem; /* Added explicit size */
}

/* Timestamp Info Styles for Create/Update Date & Time */
.timestamp-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
  margin-top: 4px;
  padding: 3px 0;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

[data-theme="dark"] .timestamp-info {
  border-top-color: rgba(255, 255, 255, 0.1);
}

.timestamp-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 0.75rem;
  color: var(--text-muted);
  padding: 1px 0;
}

.timestamp-item i {
  font-size: 0.7rem;
  width: 12px;
  text-align: center;
}

.timestamp-item.create-time i {
  color: var(--secondary-color);
}

.timestamp-item.update-time i {
  color: var(--warning-color);
}

.timestamp-item span {
  font-family: var(--bengali-font);
  font-weight: 400;
  line-height: 1.1;
}

/* Responsive adjustments for timestamp info */
@media (max-width: 768px) {
  .timestamp-info {
    gap: 1px;
    margin-top: 3px;
    padding: 2px 0;
  }

  .timestamp-item {
    font-size: 0.7rem;
    gap: 3px;
    padding: 0;
  }

  .timestamp-item i {
    font-size: 0.65rem;
    width: 10px;
  }

  .transaction-item {
    padding: 8px 10px;
    margin-bottom: 6px;
  }

  .transaction-info h4 {
    font-size: 1.2rem;
    margin-bottom: 2px;
  }

  .transaction-info p {
    font-size: 0.9rem;
    margin: 0;
  }

  .transaction-amount {
    font-size: 1.6rem;
    margin-top: 10px;
  }
}

/* Edit Modal Timestamp Display */
.edit-timestamp-display {
  background: var(--light-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 12px;
  margin-top: 8px;
}

[data-theme="dark"] .edit-timestamp-display {
  background: #2c2c2c;
  border-color: #444;
}

.edit-timestamp-display .timestamp-info {
  margin-top: 0;
  padding: 0;
  border-top: none;
}

.edit-timestamp-display .timestamp-item {
  font-size: 0.85rem;
  padding: 4px 0;
}

.edit-timestamp-display .timestamp-item i {
  font-size: 0.8rem;
  width: 14px;
}

/* Detail Modal Timestamp Section */
.detail-timestamp-section {
  width: 100%;
  margin: 8px 0;
}

.detail-timestamp-section .timestamp-info {
  background: rgba(0, 0, 0, 0.05);
  border-radius: var(--border-radius);
  padding: 8px 12px;
  margin-top: 0;
  border-top: none;
}

[data-theme="dark"] .detail-timestamp-section .timestamp-info {
  background: rgba(255, 255, 255, 0.05);
}

.detail-timestamp-section .timestamp-item {
  font-size: 0.8rem;
  padding: 3px 0;
}

.detail-timestamp-section .timestamp-item i {
  font-size: 0.75rem;
  width: 12px;
}

/* Header controls are now handled in .header-content */

/* Header Color Button */
.header-color-btn {
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 0;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px; /* Increased size */
  height: 36px;
  font-size: 14px; /* Increased font size */
}

.header-color-btn:hover {
  background: rgba(255, 255, 255, 0.25);
  border-color: rgba(255, 255, 255, 0.4);
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* Global Search Button */
.global-search-btn {
    background: rgba(255, 255, 255, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    color: white;
    font-size: 13px;
}

.global-search-btn:hover {
  background: rgba(255, 255, 255, 0.25);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.global-search-btn:active {
  transform: translateY(0);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.global-search-btn i {
  font-size: 11px;
}

.global-search-btn span {
  font-weight: 500;
}

/* Fullscreen Button */
.fullscreen-btn {
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  width: 45px; /* Increased size */
  height: 45px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px; /* Increased font size */
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.fullscreen-btn:hover {
  background: rgba(255, 255, 255, 0.25);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.fullscreen-btn:active {
  transform: translateY(0);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.fullscreen-btn.exit-fullscreen i {
  transform: rotate(45deg);
}

/* Notification Bell */
.notification-bell {
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 0;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px; /* Increased size */
  height: 36px;
  backdrop-filter: blur(10px);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.notification-bell:hover {
  background: rgba(255, 255, 255, 0.25);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.notification-bell:active {
  transform: translateY(0);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.notification-bell i {
  font-size: 14px; /* Increased icon size */
}

.notification-bell.has-notifications {
  animation: bellShake 0.8s ease-in-out;
}

.notification-bell:active {
  animation: bellRing 0.3s ease-in-out;
}

.notification-badge {
  position: absolute;
  top: -2px;
  right: -2px;
  background: #e74c3c;
  color: white;
  border-radius: 50%;
  width: 18px; /* Increased badge size */
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 8px; /* Smaller font */
  font-weight: bold;
  font-family: var(--number-font);
  opacity: 0;
  transform: scale(0);
  transition: all 0.3s ease;
}

.notification-badge.show {
  opacity: 1;
  transform: scale(1);
}

@keyframes bellShake {
  0%, 100% { transform: rotate(0deg); }
  10% { transform: rotate(-8deg); }
  20% { transform: rotate(8deg); }
  30% { transform: rotate(-6deg); }
  40% { transform: rotate(6deg); }
  50% { transform: rotate(-4deg); }
  60% { transform: rotate(4deg); }
  70% { transform: rotate(-2deg); }
  80% { transform: rotate(2deg); }
  90% { transform: rotate(-1deg); }
}

@keyframes bellRing {
  0% { transform: rotate(0deg) scale(1); }
  25% { transform: rotate(-15deg) scale(1.1); }
  50% { transform: rotate(15deg) scale(1.1); }
  75% { transform: rotate(-10deg) scale(1.05); }
  100% { transform: rotate(0deg) scale(1); }
}

/* Notification Panel */
.notification-panel {
  position: fixed;
  top: 0;
  left: -450px;
  width: 420px;
  height: 100vh;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-right: 1px solid rgba(0, 0, 0, 0.1);
  z-index: 9999;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  box-shadow: 8px 0 32px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  transform: translateX(-100%);
}

.notification-panel.show {
  left: 0;
  transform: translateX(0);
  animation: slideInPanelFromLeft 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.notification-panel.hide {
  left: -450px;
  transform: translateX(-100%);
  animation: slideOutPanelToLeft 0.3s ease-in;
}

.notification-header {
  padding: 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.notification-header h3 {
  margin: 0;
  font-family: var(--bengali-font);
  font-size: 22px; /* Increased from 18px */
  font-weight: 400;
}

.close-notification-panel {
  background: none;
  border: none;
  color: white;
  font-size: 18px;
  cursor: pointer;
  padding: 5px;
  border-radius: 50%;
  transition: background 0.3s ease;
}

.close-notification-panel:hover {
  background: rgba(255, 255, 255, 0.2);
}

.notification-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.notification-tabs {
  display: flex;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  background: white;
}

.notification-tab {
  flex: 1;
  padding: 15px;
  border: none;
  background: none;
  cursor: pointer;
  font-family: var(--bengali-font);
  font-size: 18px; /* Increased from 14px */
  font-weight: 400;
  color: #666;
  transition: all 0.3s ease;
  border-bottom: 3px solid transparent;
}

.notification-tab.active {
  color: #667eea;
  border-bottom-color: #667eea;
  background: rgba(102, 126, 234, 0.05);
}

.notification-tab:hover {
  background: rgba(102, 126, 234, 0.1);
}

.notification-list {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
}

.notification-item {
  background: white;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 10px;
  transition: all 0.3s ease;
  position: relative;
  display: flex;
  align-items: flex-start;
  gap: 10px;
}

.notification-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.notification-item.unread {
  border-left: 4px solid #667eea;
  background: rgba(102, 126, 234, 0.02);
}

.notification-item-content-wrapper {
  flex: 1;
  cursor: pointer;
}

.notification-item-actions {
  display: flex;
  flex-direction: column;
  gap: 5px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.notification-item:hover .notification-item-actions {
  opacity: 1;
}

.notification-delete-btn {
  background: rgba(231, 76, 60, 0.1);
  border: none;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #e74c3c;
  font-size: 12px;
  transition: all 0.3s ease;
}

.notification-delete-btn:hover {
  background: #e74c3c;
  color: white;
  transform: scale(1.1);
}

.notification-item.unread::before {
  content: '';
  position: absolute;
  top: 10px;
  right: 10px;
  width: 8px;
  height: 8px;
  background: #e74c3c;
  border-radius: 50%;
}

.notification-item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
  gap: 10px;
  flex-direction: column;
  
}

.notification-item-title {
  font-weight: 400;
  color: #333;
  font-family: var(--bengali-font);
  font-size: 22px; /* Increased from 14px */
  flex: 1;
  margin-right: 10px;
}

.notification-item-time {
  font-size: 16px; /* Increased from 12px */
  color: #999;
  font-family: var(--number-font);
  text-align: right;
  line-height: 1.3;
}

.notification-item-time .time-ago {
  font-weight: 500;
  color: #667eea;
  margin-bottom: 2px;
  font-size: 13px;
}

.notification-item-time .full-datetime {
  font-size: 15px; /* Increased from 10px */
  color: #666;
  font-family: var(--bengali-font);
}

.notification-item-content {
  color: #666;
  font-size: 17px; /* Increased from 13px */
  line-height: 1.4;
  font-family: var(--bengali-font);
}

.notification-item-type {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  margin-top: 8px;
  font-family: var(--english-font);
}

.notification-item-type.reminder {
  background: rgba(243, 156, 18, 0.1);
  color: #f39c12;
}

.notification-item-type.transaction {
  background: rgba(46, 204, 113, 0.1);
  color: #2ecc71;
}

.notification-item-type.overdue {
  background: rgba(231, 76, 60, 0.1);
  color: #e74c3c;
}

.notification-actions {
  padding: 15px 20px;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  display: flex;
  gap: 10px;
  background: white;
}

.notification-actions .btn {
  flex: 1;
  padding: 10px;
  font-size: 12px;
}

/* Empty notification state */
.notification-empty {
  text-align: center;
  padding: 40px 20px;
  color: #999;
  font-family: var(--bengali-font);
  font-size: 18px; /* Added larger font size */
}

.notification-empty i {
  font-size: 48px;
  margin-bottom: 15px;
  opacity: 0.5;
}

/* Search Modal Styles */
.search-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10000;
  display: none;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.search-modal.show {
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 1;
}

.search-modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(5px);
  animation: fadeIn 0.3s ease;
}

.search-modal-content {
  position: relative;
  background: var(--bg-color);
  border-radius: 12px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  width: 80%;
  height: 80%;
  max-width: 1200px;
  max-height: 800px;
  min-width: 600px;
  min-height: 500px;
  overflow: hidden;
  animation: modalSlideIn 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  border: 2px solid var(--border-color);
  resize: both;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
}

.search-modal-content.fullscreen {
  width: 100% !important;
  height: 100% !important;
  max-width: none !important;
  max-height: none !important;
  border-radius: 0 !important;
  border: none !important;
  resize: none !important;
}

/* Custom resize handle */
.search-modal-content::after {
  content: "";
  position: absolute;
  bottom: 0;
  right: 0;
  width: 20px;
  height: 20px;
  background: linear-gradient(
    -45deg,
    transparent 0%,
    transparent 30%,
    var(--primary-color) 30%,
    var(--primary-color) 35%,
    transparent 35%,
    transparent 65%,
    var(--primary-color) 65%,
    var(--primary-color) 70%,
    transparent 70%
  );
  cursor: nw-resize;
  border-bottom-right-radius: 15px;
}

/* Resize handle hover effect */
.search-modal-content:hover::after {
  background: linear-gradient(
    -45deg,
    transparent 0%,
    transparent 30%,
    var(--secondary-color) 30%,
    var(--secondary-color) 35%,
    transparent 35%,
    transparent 65%,
    var(--secondary-color) 65%,
    var(--secondary-color) 70%,
    transparent 70%
  );
}

/* Resize indicator tooltip */
.search-modal-content::before {
  content: "টেনে আকার পরিবর্তন করুন";
  position: absolute;
  bottom: 25px;
  right: 5px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
  white-space: nowrap;
  z-index: 1000;
}

.search-modal-content:hover::before {
  opacity: 1;
}

.search-modal-header {
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--secondary-color)
  );
  color: white;
  padding: 20px 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 0;
  flex-shrink: 0;
}

.search-modal-header h3 {
  margin: 0;
  font-size: 22px; /* Increased from 18px */
  font-weight: 400;
  display: flex;
  align-items: center;
  gap: 10px;
}

.search-modal-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.search-modal-fullscreen,
.search-modal-close {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  width: 35px;
  height: 35px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.search-modal-fullscreen:hover,
.search-modal-close:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

/* Resize indicator tooltip */
.search-modal-content::before {
  content: "টেনে আকার পরিবর্তন করুন";
  position: absolute;
  bottom: 25px;
  right: 5px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 5px 10px;
  border-radius: 5px;
  font-size: 12px;
  font-family: var(--bengali-font);
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease;
  z-index: 1000;
}

.search-modal-content:hover::before {
  opacity: 1;
}

.search-modal-content.fullscreen::before {
  display: none;
}

/* Custom resize handle */
.search-modal-content::after {
  content: '';
  position: absolute;
  bottom: 0;
  right: 0;
  width: 20px;
  height: 20px;
  background: linear-gradient(-45deg, transparent 0%, transparent 30%, var(--primary-color) 30%, var(--primary-color) 40%, transparent 40%, transparent 60%, var(--primary-color) 60%, var(--primary-color) 70%, transparent 70%);
  cursor: nw-resize;
  z-index: 1001;
}

.search-modal-content.fullscreen::after {
  display: none;
}

.search-modal-search {
  padding: 20px 30px;
  border-bottom: 1px solid var(--border-color);
  background: var(--light-color);
  flex-shrink: 0;
}

/* Category Filter Styles */
.category-filter-section {
  margin-top: 15px;
}

.category-filter-btn {
  background: linear-gradient(135deg, var(--success-color), #27ae60);
  color: #3498db;
  border: none;
  padding: 12px 20px;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 10px;
  font-weight: 600;
  transition: all 0.3s ease;
  width: 100%;
  justify-content: space-between;
}

.category-filter-btn:hover {
  background: linear-gradient(135deg, #27ae60, var(--success-color));
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(39, 174, 96, 0.3);
}

.category-filter-content {
  background: #ffffff;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  margin-top: 10px;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.category-type-tabs {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 15px;
}

.category-type-tab {
  background: transparent;
  border: 2px solid var(--border-color);
  color: var(--text-color);
  padding: 8px 16px;
  border-radius: 20px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
}

.category-type-tab.active,
.category-type-tab:hover {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.category-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 10px;
  margin-bottom: 20px;
  max-height: 300px;
  overflow-y: auto;
}

.category-item-filter {
  background: #f8f9fa;
  border: 2px solid var(--border-color);
  color: #2c3e50;
  padding: 12px 16px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 10px;
  font-weight: 500;
}

.category-item-filter:hover {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.category-item-filter.selected {
  background: var(--success-color);
  color: #3498db;
  border-color: var(--success-color);
}

.category-filter-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}

/* Date Filter Styles */
.date-filter-section {
  margin-top: 15px;
}

.date-filter-btn {
  width: 100%;
  background: var(--bg-color);
  border: 1px solid var(--border-color);
  color: var(--text-color);
  padding: 12px 15px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-family: var(--bengali-font);
  font-size: 16px;
}

.date-filter-btn:hover {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.date-filter-btn.active {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.date-filter-btn i:first-child {
  margin-right: 8px;
}

.date-filter-btn i:last-child {
  transition: transform 0.3s ease;
}

.date-filter-btn.active i:last-child {
  transform: rotate(180deg);
}

.date-filter-content {
  margin-top: 10px;
  padding: 15px;
  background: var(--bg-color);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  animation: slideDown 0.3s ease;
}

.quick-date-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 15px;
}

.quick-date-btn {
  background: var(--light-color);
  border: 1px solid var(--border-color);
  color: var(--text-color);
  padding: 8px 12px;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: var(--bengali-font);
  font-size: 12px;
  font-weight: 500;
}

.quick-date-btn:hover {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
  transform: translateY(-2px);
}

.quick-date-btn.active {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.custom-date-range {
  border-top: 1px solid var(--border-color);
  padding-top: 15px;
}

.date-input-group {
  margin-bottom: 12px;
}

.date-input-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: var(--text-color);
  font-size: 13px;
}

.date-input {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  background: var(--bg-color);
  color: var(--text-color);
  font-family: var(--bengali-font);
  font-size: 14px;
  transition: all 0.3s ease;
}

.date-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.apply-date-filter {
  width: 100%;
  background: var(--primary-color);
  border: none;
  color: white;
  padding: 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: var(--bengali-font);
  font-size: 14px;
  font-weight: 600;
  margin-top: 10px;
}

.apply-date-filter:hover {
  background: var(--secondary-color);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modal-search-container {
  position: relative;
  display: flex;
  align-items: center;
  background: var(--bg-color);
  border: 2px solid var(--border-color);
  border-radius: 25px;
  padding: 12px 20px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.modal-search-container:focus-within {
  border-color: var(--primary-color);
  box-shadow: 0 8px 25px rgba(52, 152, 219, 0.2);
  transform: translateY(-2px);
}

#modalSearchInput {
  flex: 1;
  background: transparent;
  border: none;
  outline: none;
  color: var(--text-color);
  font-size: 20px; /* Increased from 16px */
  font-family: var(--bengali-font);
  padding: 8px 15px;
}

#modalSearchInput::placeholder {
  color: var(--text-color);
  opacity: 0.5;
}

.modal-search-btn,
.modal-search-clear {
  background: var(--primary-color);
  border: none;
  color: white;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 35px;
  height: 35px;
  margin-left: 10px;
}

.modal-search-btn:hover,
.modal-search-clear:hover {
  background: var(--secondary-color);
  transform: scale(1.1);
}

.modal-search-clear {
  background: var(--danger-color);
}

.search-modal-body {
  padding: 0;
  height: auto;
  overflow: visible;
  min-height: 200px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.search-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: var(--primary-color);
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid var(--light-color);
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

.search-results-container {
  padding: 0;
  height: auto;
  min-height: 200px;
  max-height: none;
  overflow-y: auto;
  resize: none;
  border-top: 2px solid var(--border-color);
  position: relative;
  flex: 1;
}

/* Custom vertical resize handle for search results */
.search-results-container::before {
  content: "⋮⋮⋮";
  position: absolute;
  top: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 16px;
  background: var(--primary-color);
  border-radius: 8px;
  cursor: ns-resize;
  opacity: 0.7;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
  font-weight: bold;
  letter-spacing: 2px;
}

.search-results-container:hover::before {
  opacity: 1;
  background: var(--secondary-color);
  transform: translateX(-50%) scale(1.1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

/* Resize indicator for search results */
.search-results-container::after {
  content: "টেনে উচ্চতা পরিবর্তন করুন";
  position: absolute;
  top: -35px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 12px;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
  white-space: nowrap;
  z-index: 1000;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.search-results-container:hover::after {
  opacity: 1;
}

/* Search Welcome Screen */
.search-welcome {
  padding: 60px 40px;
  text-align: center;
  color: var(--text-color);
  animation: fadeIn 0.5s ease;
}

.search-welcome i {
  font-size: 64px;
  color: var(--primary-color);
  margin-bottom: 20px;
  opacity: 0.7;
  animation: pulse 2s infinite;
}

.search-welcome h4 {
  font-size: 24px;
  margin-bottom: 10px;
  color: var(--text-color);
  font-weight: 600;
}

.search-welcome p {
  font-size: 16px;
  opacity: 0.7;
  margin-bottom: 30px;
}

.search-suggestions {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  justify-content: center;
  margin-top: 20px;
}

.suggestion-tag {
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--secondary-color)
  );
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  font-family: var(--bengali-font);
  font-weight: 500;
  box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.suggestion-tag:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 8px 25px rgba(52, 152, 219, 0.4);
}

.suggestion-tag:active {
  transform: translateY(-1px) scale(1.02);
}

.date-suggestion {
  background: linear-gradient(135deg, #ff6b6b, #ee5a24) !important;
}

.date-suggestion:hover {
  background: linear-gradient(135deg, #ee5a24, #ff6b6b) !important;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 0.7;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
}

.search-result-item {
  padding: 10px 15px;
  border-bottom: 1px solid var(--border-color);
  background: var(--bg-color);
  color: var(--text-color);
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  display: flex;
  align-items: center;
  gap: 12px;
  position: relative;
  overflow: hidden;
  opacity: 0;
  transform: translateY(20px);
  animation: resultItemSlideIn 0.5s ease forwards;
}

.search-result-item:nth-child(1) {
  animation-delay: 0.1s;
}
.search-result-item:nth-child(2) {
  animation-delay: 0.15s;
}
.search-result-item:nth-child(3) {
  animation-delay: 0.2s;
}
.search-result-item:nth-child(4) {
  animation-delay: 0.25s;
}
.search-result-item:nth-child(5) {
  animation-delay: 0.3s;
}
.search-result-item:nth-child(6) {
  animation-delay: 0.35s;
}
.search-result-item:nth-child(7) {
  animation-delay: 0.4s;
}
.search-result-item:nth-child(8) {
  animation-delay: 0.45s;
}

.search-result-item::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s ease;
}

.search-result-item:hover::before {
  left: 100%;
}

.search-result-item:hover {
  background: var(--light-color);
  transform: translateX(10px) scale(1.02);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border-left: 4px solid var(--primary-color);
}

.search-result-item:last-child {
  border-bottom: none;
}

.search-result-icon {
  width: 45px;
  height: 45px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  color: white;
  flex-shrink: 0;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.search-result-icon::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.2);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.search-result-item:hover .search-result-icon::before {
  opacity: 1;
}

.search-result-item:hover .search-result-icon {
  transform: scale(1.1) rotate(5deg);
}

.search-result-icon.income {
  background: linear-gradient(135deg, #28a745, #20c997);
}

.search-result-icon.expense {
  background: linear-gradient(135deg, #dc3545, #fd7e14);
}

.search-result-icon.loan-given {
  background: linear-gradient(135deg, #ffc107, #fd7e14);
}

.search-result-icon.loan-taken {
  background: linear-gradient(135deg, #6f42c1, #e83e8c);
}

.search-result-icon.bank-deposit {
  background: linear-gradient(135deg, #17a2b8, #6f42c1);
}

.search-result-icon.bank-withdraw {
  background: linear-gradient(135deg, #dc3545, #6f42c1);
}

.search-result-content {
  flex: 1;
  min-width: 0;
}

.search-result-title {
  font-weight: 400;
  color: #2c3e50;
  margin-bottom: 5px;
  font-size: 20px; /* Increased from 16px */
  line-height: 1.3;
}

.search-result-description {
  color: #34495e;
  font-size: 17px; /* Increased from 13px */
  margin-bottom: 8px;
  line-height: 1.4;
}

.search-result-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 16px; /* Increased from 12px */
  color: #7f8c8d;
}

.search-result-amount {
  font-weight: 500;
  color: var(--primary-color);
  font-size: 20px;
  padding: 8px 15px;
  background: rgba(52, 152, 219, 0.1);
  border: 1px solid rgba(52, 152, 219, 0.2);
}

.search-no-results {
  padding: 60px 20px;
  text-align: center;
  color: #2c3e50;
  animation: fadeIn 0.5s ease;
}

.search-no-results i {
  font-size: 48px;
  margin-bottom: 15px;
  display: block;
  opacity: 0.3;
  color: #7f8c8d;
}

.search-no-results h3 {
  color: #2c3e50;
  margin: 15px 0 10px 0;
  font-size: 18px;
  font-weight: 600;
}

.search-no-results p {
  color: #34495e;
  font-size: 14px;
  margin: 10px 0;
}

.search-category-header {
  padding: 15px 20px;
  background: linear-gradient(
    135deg,
    #f8f9fa,
    rgba(52, 152, 219, 0.1)
  );
  font-weight: 600;
  font-size: 13px;
  color: #2c3e50;
  text-transform: uppercase;
  letter-spacing: 1px;
  border-bottom: 2px solid var(--primary-color);
  position: sticky;
  top: 0;
  z-index: 10;
  backdrop-filter: blur(10px);
  animation: categorySlideIn 0.4s ease;
}

.search-category-header::before {
  content: "";
  position: absolute;
  left: 0;
  bottom: 0;
  width: 50px;
  height: 2px;
  background: var(--primary-color);
  animation: categoryLineExpand 0.6s ease;
}





/* Dark theme search adjustments */
[data-theme="dark"] .search-results {
  background: #2c2c2c;
  border-color: #444444;
}

[data-theme="dark"] .search-result-item:hover {
  background: #3c3c3c;
}

[data-theme="dark"] .search-category-header {
  background: linear-gradient(
    135deg,
    #3c3c3c,
    rgba(52, 152, 219, 0.2)
  );
  color: #ecf0f1;
}

/* Dark mode text colors for search results */
[data-theme="dark"] .search-result-title {
  color: #ecf0f1;
}

[data-theme="dark"] .search-result-description {
  color: #bdc3c7;
}

[data-theme="dark"] .search-result-meta {
  color: #95a5a6;
}

/* Dark mode for no results message */
[data-theme="dark"] .search-no-results {
  color: #ecf0f1;
}

[data-theme="dark"] .search-no-results i {
  color: #95a5a6;
}

[data-theme="dark"] .search-no-results h3 {
  color: #ecf0f1;
}

[data-theme="dark"] .search-no-results p {
  color: #bdc3c7;
}

/* Dark theme for search category header */
[data-theme="dark"] .search-category-header {
  background: linear-gradient(
    135deg,
    #3c3c3c,
    rgba(52, 152, 219, 0.2)
  );
  color: #ecf0f1;
  border-color: #444444;
}



/* Dark theme for form inputs */
[data-theme="dark"] .form-group input,
[data-theme="dark"] .form-group select,
[data-theme="dark"] .form-group textarea {
  background: #2c2c2c !important;
  color: #ffffff !important;
  border-color: #555555 !important;
}

[data-theme="dark"] .form-group input:focus,
[data-theme="dark"] .form-group select:focus,
[data-theme="dark"] .form-group textarea:focus {
  border-color: var(--primary-color);
  background: #333333;
}

[data-theme="dark"] .form-group input::placeholder,
[data-theme="dark"] .form-group textarea::placeholder {
  color: #aaaaaa !important;
}

/* General placeholder styles */
.form-group input::placeholder,
.form-group textarea::placeholder {
  color: #666666 !important;
  opacity: 1 !important;
}

/* Force input field styles */
input[type="text"],
input[type="number"],
input[type="date"],
input[type="email"],
textarea,
select {
  background: var(--bg-color) !important;
  color: var(--text-color) !important;
  border: 2px solid var(--border-color) !important;
  padding: 12px !important;
  font-family: var(--bengali-font) !important;
  font-size: 1rem !important;
  pointer-events: auto !important;
  user-select: text !important;
  -webkit-user-select: text !important;
  -moz-user-select: text !important;
  cursor: text !important;
}

input[type="text"]:focus,
input[type="number"]:focus,
input[type="date"]:focus,
input[type="email"]:focus,
textarea:focus,
select:focus {
  outline: none !important;
  border-color: var(--primary-color) !important;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1) !important;
}

[data-theme="dark"] .form-group label {
  color: #ffffff;
}

/* Dark theme force input styles */
[data-theme="dark"] input[type="text"],
[data-theme="dark"] input[type="number"],
[data-theme="dark"] input[type="date"],
[data-theme="dark"] input[type="email"],
[data-theme="dark"] textarea,
[data-theme="dark"] select {
  background: #2c2c2c !important;
  color: #ffffff !important;
  border-color: #555555 !important;
}

[data-theme="dark"] input[type="text"]:focus,
[data-theme="dark"] input[type="number"]:focus,
[data-theme="dark"] input[type="date"]:focus,
[data-theme="dark"] input[type="email"]:focus,
[data-theme="dark"] textarea:focus,
[data-theme="dark"] select:focus {
  background: #333333 !important;
  border-color: var(--primary-color) !important;
}

/* Dark mode for category filter items */
[data-theme="dark"] .category-item-filter {
  background: #3c3c3c;
  color: #ecf0f1;
  border-color: #555;
}

[data-theme="dark"] .category-item-filter:hover {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

[data-theme="dark"] .category-item-filter.selected {
  background: var(--success-color);
  color: white;
  border-color: var(--success-color);
}

/* Dark mode for category filter content */
[data-theme="dark"] .category-filter-content {
  background: #2c2c2c;
  border-color: #555;
}

/* Dark theme for notification system */
[data-theme="dark"] .notification-panel {
  background: rgba(44, 44, 44, 0.95);
  border-right-color: #555;
  box-shadow: 8px 0 32px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .notification-header {
  background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
}

[data-theme="dark"] .notification-content {
  background: #2c2c2c;
}

[data-theme="dark"] .notification-tabs {
  background: #2c2c2c;
  border-bottom-color: #555;
}

[data-theme="dark"] .notification-tab {
  color: #bdc3c7;
}

[data-theme="dark"] .notification-tab.active {
  color: #3498db;
  background: rgba(52, 152, 219, 0.1);
  border-bottom-color: #3498db;
}

[data-theme="dark"] .notification-tab:hover {
  background: rgba(52, 152, 219, 0.15);
}

[data-theme="dark"] .notification-item {
  background: #3c3c3c;
  border-color: #555;
}

[data-theme="dark"] .notification-item:hover {
  background: #444;
}

[data-theme="dark"] .notification-item.unread {
  border-left-color: #3498db;
  background: rgba(52, 152, 219, 0.05);
}

[data-theme="dark"] .notification-item-title {
  color: #ecf0f1;
}

[data-theme="dark"] .notification-item-content {
  color: #bdc3c7;
}

[data-theme="dark"] .notification-actions {
  background: #2c2c2c;
  border-top-color: #555;
}

[data-theme="dark"] .notification-empty {
  color: #7f8c8d;
}

[data-theme="dark"] .notification-bell {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
}

[data-theme="dark"] .notification-bell:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
}

/* Dark theme for period selectors */
[data-theme="dark"] .period-selector {
  background: #2c2c2c !important;
  color: #ffffff !important;
  border-color: #555555 !important;
  cursor: pointer !important;
}

[data-theme="dark"] .period-selector:hover {
  background: #333333 !important;
  border-color: var(--primary-color) !important;
  cursor: pointer !important;
}

[data-theme="dark"] .period-selector:focus {
  background: #333333 !important;
  border-color: var(--primary-color) !important;
  cursor: pointer !important;
}

[data-theme="dark"] .period-selector option {
  background: #2c2c2c !important;
  color: #ffffff !important;
}

/* Dark theme for all select elements */
[data-theme="dark"] select {
  background: #2c2c2c !important;
  color: #ffffff !important;
  border-color: #555555 !important;
  cursor: pointer !important;
}

[data-theme="dark"] select:hover {
  background: #333333 !important;
  cursor: pointer !important;
}

[data-theme="dark"] select:focus {
  background: #333333 !important;
  cursor: pointer !important;
}

[data-theme="dark"] select option {
  background: #2c2c2c !important;
  color: #ffffff !important;
}

/* Search Modal Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(-50px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes resultItemSlideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes categorySlideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes categoryLineExpand {
  from {
    width: 0;
  }
  to {
    width: 50px;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Search highlight animation */
.search-highlight {
  background: linear-gradient(
    90deg,
    rgba(52, 152, 219, 0.2) 0%,
    rgba(52, 152, 219, 0.4) 50%,
    rgba(52, 152, 219, 0.2) 100%
  );
  border: 2px solid var(--primary-color);
  border-radius: var(--border-radius);
  animation: searchHighlight 3s ease-in-out;
  transform: scale(1.02);
  box-shadow: 0 8px 25px rgba(52, 152, 219, 0.3);
}

@keyframes searchHighlight {
  0% {
    background: rgba(52, 152, 219, 0.6);
    transform: scale(1.05);
  }
  50% {
    background: rgba(52, 152, 219, 0.4);
    transform: scale(1.02);
  }
  100% {
    background: rgba(52, 152, 219, 0.2);
    transform: scale(1);
  }
}

[data-theme="dark"] .search-highlight {
  background: linear-gradient(
    90deg,
    rgba(52, 152, 219, 0.3) 0%,
    rgba(52, 152, 219, 0.5) 50%,
    rgba(52, 152, 219, 0.3) 100%
  );
  box-shadow: 0 8px 25px rgba(52, 152, 219, 0.4);
}

/* Responsive search */
@media (max-width: 768px) {
  .global-search-btn {
    padding: 10px 16px;
    font-size: 13px;
  }

  .global-search-btn i {
    font-size: 14px;
  }

  .global-search-btn span {
    display: none; /* Hide text on mobile, show only icon */
  }

  .fullscreen-btn {
    width: 45px;
    height: 45px;
    font-size: 16px;
  }

  .search-modal-content {
    width: 95% !important;
    height: 95% !important;
    max-width: none !important;
    max-height: 95vh !important;
    margin: 10px;
    min-width: 300px !important;
    min-height: 250px !important;
    border-radius: 8px !important;
    resize: none !important;
  }

  .search-modal-content.fullscreen {
    width: 100% !important;
    height: 100% !important;
    margin: 0 !important;
    border-radius: 0 !important;
  }

  .search-modal-body {
    max-height: calc(95vh - 160px);
    min-height: 200px;
  }

  .search-results-container {
    height: 300px;
    min-height: 150px;
    max-height: 70vh;
  }

  .search-modal-header {
    padding: 15px;
  }

  .search-modal-header h3 {
    font-size: 16px;
  }

  .search-modal-search {
    padding: 15px;
  }

  .modal-search-container {
    padding: 10px 15px;
  }

  #modalSearchInput {
    font-size: 14px;
    padding: 6px 10px;
  }

  .modal-search-btn,
  .modal-search-clear {
    width: 30px;
    height: 30px;
    padding: 6px;
  }

  .search-welcome {
    padding: 40px 20px;
  }

  .search-welcome i {
    font-size: 48px;
  }

  .search-welcome h4 {
    font-size: 20px;
  }

  .search-welcome p {
    font-size: 14px;
  }

  .suggestion-tag {
    font-size: 12px;
    padding: 6px 12px;
  }

  .search-result-item {
    padding: 8px 12px;
    gap: 10px;
  }

  .search-result-icon {
    width: 40px;
    height: 40px;
    font-size: 14px;
  }

  .search-result-title {
    font-size: 14px;
  }

  .search-result-description {
    font-size: 12px;
  }

  .search-result-amount {
    font-size: 12px;
    padding: 3px 6px;
  }

  .search-category-header {
    padding: 12px 15px;
    font-size: 12px;
  }


}

.theme-toggle {
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 0;
  border-radius: 50%;
  cursor: pointer;
  transition: var(--transition);
  font-size: 14px; /* Increased font size */
  width: 36px; /* Increased size */
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.theme-toggle:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* Balance Container */
.balance-container {
  display: flex;
  align-items: center;
  gap: 4px; /* Even smaller gap */
  flex-direction: row-reverse;
}

.balance-toggle {
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  width: 36px; /* Increased size */
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  color: white;
  font-size: 13px; /* Increased font size */
}

.balance-toggle:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.15);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.balance-toggle.hidden {
  background: rgba(231, 76, 60, 0.8);
  animation: hiddenPulse 2s ease-in-out infinite;
}

.balance-toggle.hidden:hover {
  background: rgba(231, 76, 60, 1);
  animation: none;
}

@keyframes hiddenPulse {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(231, 76, 60, 0.7);
  }
  50% {
    box-shadow: 0 0 0 8px rgba(231, 76, 60, 0);
  }
}

/* Balance visibility states */
.current-balance.hidden {
  opacity: 0;
  transform: translateX(100px);
  pointer-events: none;
  visibility: hidden;
}

/* Balance reveal animation */
.current-balance.reveal {
  animation: balanceSlideInFromRight 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

@keyframes balanceSlideInFromRight {
  0% {
    opacity: 0;
    transform: translateX(100px);
    visibility: hidden;
  }
  100% {
    opacity: 1;
    transform: translateX(0);
    visibility: visible;
  }
}

.current-balance {
    font-size: 1.3rem;
    font-weight: 600;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 30px 30px;
    border-radius: 18px;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    gap: 12px;
    height: 48px;
    white-space: nowrap;
    transform: translateX(0);
    margin-right: 10px;
}

.current-balance i {
  font-size: 20px; /* Further increased icon size */
  color: #f1c40f;
  animation: coinSpin 3s linear infinite;
}

@keyframes coinSpin {
  0% {
    transform: rotateY(0deg);
  }
  50% {
    transform: rotateY(180deg);
  }
  100% {
    transform: rotateY(360deg);
  }
}

.current-balance::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.current-balance:hover::before {
  left: 100%;
}

.current-balance:hover {
  transform: translateY(-3px) scale(1.02);
  box-shadow: 0 15px 40px rgba(102, 126, 234, 0.5);
}

.balance-amount {
  font-size: 2.1rem; /* Further increased font size */
  font-weight: 700;
  color: #ffffff;
  text-shadow: 0 1px 4px rgba(0, 0, 0, 0.3);
  margin-left: 0; /* Remove margin */
  display: inline-block;
  animation: balanceGlow 2s ease-in-out infinite alternate;
}

@keyframes balanceGlow {
  0% {
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3),
      0 0 20px rgba(255, 255, 255, 0.5);
  }
  100% {
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3),
      0 0 30px rgba(255, 255, 255, 0.8);
  }
}

/* Navigation Styles */
.nav-tabs {
  display: flex;
  background: var(--light-color);
  border-radius: var(--border-radius);
  padding: 5px;
  margin-bottom: 20px;
  box-shadow: var(--shadow);
  overflow-x: auto;
  animation: slideInUp 0.5s ease;
  position: sticky;
  top: 105px; /* Position below the larger sticky header with some gap */
  z-index: 998; /* Lower than header z-index */
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  z-index: 100;
}

.nav-tab {
  flex: 1;
  background: transparent;
  border: none;
  padding: 15px 20px;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  font-weight: 400;
  font-size: 20px; /* Added larger font size */
  color: var(--text-color);
  white-space: nowrap;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.nav-tab:hover {
  background: rgba(52, 152, 219, 0.1);
  transform: translateY(-2px);
}

.nav-tab.active {
  background: var(--primary-color);
  color: white;
  box-shadow: var(--shadow);
}

/* Main Content */
.main-content {
  animation: fadeIn 0.5s ease;
  padding-bottom: 200px; /* Space for sticky dashboard-grid */
}

.tab-content {
  display: none;
  padding-top: 10px; /* Add space below sticky nav */
}

.tab-content.active {
  display: block;
  animation: fadeInUp 0.3s ease;
}

/* Dashboard tab specific styling */
.tab-content#dashboard {
  padding-bottom: 250px; /* Extra space for sticky dashboard-grid */
}

/* Dashboard Grid - Fixed at Bottom */
.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 0;
  position: fixed;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 100%;
  max-width: 1400px;
  background: var(--bg-color);
  padding: 20px;
  border-top: 2px solid var(--border-color);
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  backdrop-filter: blur(10px);
  border-radius: 20px 20px 0 0;
  transition: all 0.3s ease;
}

/* Dashboard Grid Toggle Button */
.dashboard-toggle {
  position: absolute;
  top: -40px;
  right: 20px;
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: 20px 20px 0 0;
  padding: 8px 15px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  font-family: var(--bengali-font);
}

.dashboard-toggle:hover {
  background: #2980b9;
  transform: translateY(-2px);
}

.dashboard-toggle i {
  margin-right: 5px;
  transition: transform 0.3s ease;
}

/* Floating Toggle Button when dashboard is hidden */
.floating-dashboard-toggle {
  position: fixed;
  bottom: 20px;
  left: 20px; /* Changed from right to left to avoid conflict */
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: 50%;
  width: 60px;
  height: 60px;
  font-size: 1.2rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  z-index: 1001;
  display: none;
  align-items: center;
  justify-content: center;
}

.floating-dashboard-toggle:hover {
  background: #2980b9;
  transform: scale(1.1);
  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.3);
}



@keyframes bounceIn {
  0% { transform: scale(0); opacity: 0; }
  50% { transform: scale(1.2); opacity: 0.8; }
  100% { transform: scale(1); opacity: 1; }
}

/* Hidden state - Completely hide dashboard grid */
.dashboard-grid.hidden {
  transform: translateX(-50%) translateY(100%); /* Completely hide */
}

.dashboard-grid.hidden .dashboard-toggle i {
  transform: rotate(180deg);
}

.stat-card {
  background: var(--light-color);
  padding: 20px;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  display: flex;
  align-items: center;
  gap: 18px;
  transition: var(--transition);
  position: relative;
  overflow: hidden;
  min-height: 90px;
}

.stat-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: var(--primary-color);
}

.stat-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

.stat-card.income-card::before {
  background: var(--secondary-color);
}

.stat-card.expense-card::before {
  background: var(--danger-color);
}

.stat-card.loan-given-card::before {
  background: var(--warning-color);
}

.stat-card.loan-taken-card::before {
  background: var(--info-color);
}

.stat-card.bank-card::before {
  background: linear-gradient(135deg, #3498db, #2ecc71);
}

.stat-icon {
  background: #8BC34A;
  width: 55px;
  height: 55px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.4rem;
  color: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  flex-shrink: 0;
}

.income-card .stat-icon {
  background: var(--secondary-color);
}

.expense-card .stat-icon {
  background: var(--danger-color);
}

.loan-given-card .stat-icon {
  background: var(--warning-color);
}

.loan-taken-card .stat-icon {
  background: var(--info-color);
}

.stat-info h3 {
  font-size: 1.2rem; /* Increased from 0.95rem */
  margin-bottom: 6px;
  opacity: 0.8;
  font-weight: 400;
  line-height: 1.3;
}

.stat-amount {
  font-size: 1.8rem; /* Increased from 1.6rem */
  font-weight: 400;
  margin: 0;
  color: var(--text-color);
  line-height: 1.2;
}

/* Dashboard Charts */
.dashboard-charts {
  margin-bottom: 40px;
  background: var(--light-color);
  padding: 30px;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
}

.dashboard-charts h2 {
  margin-bottom: 25px;
  color: var(--text-color);
  font-size: 1.5rem;
  display: flex;
  align-items: center;
  gap: 10px;
}

.charts-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 25px;
}

.chart-card.full-width {
  grid-column: 1 / -1;
  margin-bottom: 30px;
}

.chart-container.large {
  height: 400px;
}

.chart-card {
  min-width: 0; /* Allow cards to shrink below their content size */
}

.chart-card.full-width {
  grid-column: 1 / -1;
  margin-bottom: 30px;
}

.chart-container.large {
  height: 400px;
}

.chart-card {
  background: var(--bg-color);
  border-radius: var(--border-radius);
  padding: 25px;
  box-shadow: var(--shadow);
  transition: var(--transition);
  border: 1px solid var(--border-color);
}

.chart-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 2px solid var(--border-color);
}

.chart-header h3 {
  margin: 0;
  color: var(--text-color);
  font-size: 1.4rem; /* Increased from 1.1rem */
  font-weight: 400;
}

.chart-period {
  display: flex;
  align-items: center;
}

.period-selector {
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  background: var(--bg-color);
  color: var(--text-color);
  font-size: 0.9rem;
  cursor: pointer !important;
  transition: var(--transition);
  position: relative;
  z-index: 10;
  pointer-events: auto !important;
}

.period-selector:hover {
  border-color: var(--primary-color);
  background: rgba(52, 152, 219, 0.05);
  cursor: pointer !important;
}

.period-selector:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
  cursor: pointer !important;
}

/* Ensure all select elements have pointer cursor */
select.period-selector,
.chart-period select,
.category-period-selector select {
  cursor: pointer !important;
  pointer-events: auto !important;
}

/* Fix for chart header positioning */
.chart-header {
  position: relative;
  z-index: 5;
}

.chart-period {
  display: flex;
  align-items: center;
  position: relative;
  z-index: 10;
}

/* Force cursor pointer on all select elements */
select {
  cursor: pointer !important;
  -webkit-appearance: menulist;
  -moz-appearance: menulist;
  appearance: menulist;
}

select:hover {
  cursor: pointer !important;
}

select:focus {
  cursor: pointer !important;
}

/* Specific targeting for period selectors */
#balanceOverviewPeriod,
#dashboardPeriod,
#incomeCategoryPeriod,
#expenseCategoryPeriod,
#monthlyTrendPeriod,
#bankTrendPeriod,
#loanAnalysisPeriod,
#loanStatusPeriod,
#progressChartPeriod,
#categoryAnalysisPeriod {
  cursor: pointer !important;
  pointer-events: auto !important;
}

/* Category period selector */
.category-period-selector {
  margin-bottom: 20px;
}

/* Searchable Dropdown Styles */
.searchable-dropdown {
  position: relative;
  width: 100%;
}

.searchable-dropdown-input {
  width: 100% !important;
  padding: 12px 40px 12px 12px !important;
  border: 2px solid var(--border-color) !important;
  border-radius: var(--border-radius) !important;
  font-size: 1.1rem !important;
  background: var(--bg-color) !important;
  color: var(--text-color) !important;
  font-family: var(--bengali-font) !important;
  cursor: pointer;
  transition: var(--transition) !important;
  box-sizing: border-box !important;
}

.searchable-dropdown-input:focus {
  outline: none;
  border-color: var(--primary-color) !important;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
  cursor: text;
}

.searchable-dropdown-input:focus + .searchable-dropdown-arrow {
  color: var(--primary-color);
}

.searchable-dropdown-arrow {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
  color: var(--text-color);
  transition: transform 0.3s ease;
  font-size: 14px;
}

.searchable-dropdown-clear {
  position: absolute;
  right: 35px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-color);
  cursor: pointer;
  font-size: 12px;
  opacity: 0;
  transition: opacity 0.3s ease;
  padding: 2px;
  border-radius: 50%;
}

.searchable-dropdown-clear:hover {
  background: var(--border-color);
  color: var(--danger-color);
}

.searchable-dropdown.has-value .searchable-dropdown-clear {
  opacity: 1;
}

/* Focus indicator */
.searchable-dropdown.open {
  position: relative;
}

.searchable-dropdown.open::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border: 2px solid var(--primary-color);
  border-radius: var(--border-radius);
  opacity: 0.3;
  pointer-events: none;
  z-index: -1;
}

.searchable-dropdown.open .searchable-dropdown-arrow {
  transform: translateY(-50%) rotate(180deg);
}

.searchable-dropdown-list {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: var(--bg-color);
  border: 2px solid var(--border-color);
  border-top: none;
  border-radius: 0 0 var(--border-radius) var(--border-radius);
  max-height: 600px;
  overflow-y: auto;
  z-index: 9999;
  visibility: hidden;
  opacity: 0;
  transform: translateY(-5px);
  transition: opacity 0.15s ease, transform 0.15s ease, visibility 0s linear 0.15s;
}

.searchable-dropdown.open .searchable-dropdown-list {
  visibility: visible;
  opacity: 1;
  transform: translateY(0);
  transition: opacity 0.15s ease, transform 0.15s ease;
}

.searchable-dropdown-item {
  padding: 12px 16px;
  cursor: pointer;
  border-bottom: 1px solid var(--border-color);
  transition: var(--transition);
  font-family: var(--bengali-font);
  font-size: 1.1rem;
  color: var(--text-color);
}

.searchable-dropdown-item:last-child {
  border-bottom: none;
}

.searchable-dropdown-item:hover,
.searchable-dropdown-item.highlighted {
  background: var(--primary-color);
  color: white;
}

.searchable-dropdown-item.selected {
  background: var(--success-color);
  color: white;
  font-weight: 600;
}

.searchable-dropdown-no-results {
  padding: 12px 16px;
  color: var(--text-color);
  opacity: 0.6;
  font-style: italic;
  text-align: center;
  font-family: var(--bengali-font);
}

/* Dark mode support */
[data-theme="dark"] .searchable-dropdown-input {
  background: #333333 !important;
  border-color: #555 !important;
  color: #ecf0f1 !important;
}

[data-theme="dark"] .searchable-dropdown-input:focus {
  background: #333333 !important;
  border-color: var(--primary-color) !important;
}

[data-theme="dark"] .searchable-dropdown-list {
  background: #333333;
  border-color: #555;
}

[data-theme="dark"] .searchable-dropdown-item {
  color: #ecf0f1;
  border-color: #555;
}

[data-theme="dark"] .searchable-dropdown-arrow {
  color: #ecf0f1;
}

[data-theme="dark"] .searchable-dropdown-no-results {
  color: #ecf0f1;
}

/* Custom scrollbar for dropdown list */
.searchable-dropdown-list::-webkit-scrollbar {
  width: 6px;
}

.searchable-dropdown-list::-webkit-scrollbar-track {
  background: var(--light-color);
  border-radius: 3px;
}

.searchable-dropdown-list::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 3px;
}

.searchable-dropdown-list::-webkit-scrollbar-thumb:hover {
  background: var(--primary-color);
}

/* Dark mode scrollbar */
[data-theme="dark"] .searchable-dropdown-list::-webkit-scrollbar-track {
  background: #444;
}

[data-theme="dark"] .searchable-dropdown-list::-webkit-scrollbar-thumb {
  background: #666;
}

[data-theme="dark"] .searchable-dropdown-list::-webkit-scrollbar-thumb:hover {
  background: var(--primary-color);
}

/* Mobile responsive */
@media (max-width: 768px) {
  .searchable-dropdown-input {
    font-size: 16px !important; /* Prevent zoom on iOS */
    padding: 14px 40px 14px 14px !important;
  }

  .searchable-dropdown-list {
    max-height: 250px;
  }

  .searchable-dropdown-item {
    padding: 14px 16px;
    font-size: 16px;
  }

  .searchable-dropdown-arrow {
    right: 14px;
  }

  .searchable-dropdown-clear {
    right: 40px;
  }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
  .searchable-dropdown-item {
    padding: 16px;
    min-height: 44px; /* iOS recommended touch target size */
    display: flex;
    align-items: center;
  }

  .searchable-dropdown-clear {
    padding: 8px;
    min-width: 32px;
    min-height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.category-period-selector select {
  cursor: pointer !important;
  pointer-events: auto !important;
}

.chart-container {
  position: relative;
  height: 300px;
  width: 100%;
}

.chart-container canvas {
  max-height: 100%;
  width: 100% !important;
  height: 100% !important;
}

/* Dark Mode Chart Styles */
[data-theme="dark"] .chart-container {
  background: rgba(255, 255, 255, 0.02);
  border-radius: 10px;
  padding: 10px;
}

[data-theme="dark"] .chart-card {
  background: var(--bg-color);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .chart-header {
  border-bottom-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .chart-header h3 {
  color: var(--text-color);
}

/* Ensure chart text is visible in dark mode */
[data-theme="dark"] canvas {
  filter: brightness(1.1) contrast(1.1);
}

/* Chart tooltip styling is handled in JavaScript */

/* Period selector styling for dark mode */
[data-theme="dark"] .period-selector {
  background: var(--bg-color);
  border-color: rgba(255, 255, 255, 0.2);
  color: var(--text-color);
}

[data-theme="dark"] .period-selector:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

/* Chart card hover effects in dark mode */
[data-theme="dark"] .chart-card:hover {
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.5);
  border-color: rgba(255, 255, 255, 0.2);
}

/* Dark Mode Category Analysis Styles */
[data-theme="dark"] .category-analysis-card {
  background: var(--bg-color);
  border-color: rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .category-analysis-card .feature-header {
  background: linear-gradient(135deg, #4a5568, #2d3748);
  color: #e2e8f0;
  border-bottom-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .category-period-selector select {
  background: var(--bg-color);
  border-color: rgba(255, 255, 255, 0.2);
  color: var(--text-color);
}

[data-theme="dark"] .category-period-selector select:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
}

[data-theme="dark"] .category-item {
  background: linear-gradient(135deg, #2d3748, #4a5568);
  border-color: rgba(255, 255, 255, 0.1);
  color: var(--text-color);
}

[data-theme="dark"] .category-item::before {
  background: linear-gradient(135deg, var(--primary-color), #3182ce);
}

[data-theme="dark"] .category-item:hover {
  border-color: var(--primary-color);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
}

[data-theme="dark"] .category-name {
  color: #e2e8f0;
}

[data-theme="dark"] .category-amount {
  color: var(--primary-color);
}

[data-theme="dark"] .category-percentage {
  background: rgba(255, 255, 255, 0.1);
  color: #cbd5e0;
}

[data-theme="dark"] .top-categories {
  background: linear-gradient(135deg, #2d3748, #4a5568);
  border-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .top-categories h4 {
  color: #e2e8f0;
  border-bottom-color: var(--primary-color);
}

[data-theme="dark"] .top-category-item {
  background: linear-gradient(135deg, #4a5568, #2d3748);
  border-color: rgba(255, 255, 255, 0.1);
  color: var(--text-color);
}

[data-theme="dark"] .top-category-item::before {
  background: linear-gradient(135deg, var(--primary-color), #3182ce);
}

[data-theme="dark"] .top-category-item:hover {
  color: white;
  box-shadow: 0 4px 15px rgba(52, 152, 219, 0.4);
}

[data-theme="dark"] .top-category-name {
  color: #e2e8f0;
}

[data-theme="dark"] .top-category-amount {
  color: var(--primary-color);
}

[data-theme="dark"] .top-category-rank {
  background: linear-gradient(135deg, var(--primary-color), #3182ce);
  box-shadow: 0 2px 8px rgba(52, 152, 219, 0.4);
}

/* Dark mode for category bar charts */
[data-theme="dark"] .category-bar {
  background: linear-gradient(135deg, #4a5568, #2d3748);
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .category-fill {
  background: linear-gradient(135deg, var(--primary-color), #3182ce);
}

[data-theme="dark"] .category-fill::after {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), transparent);
}

/* Dark mode for category header */
[data-theme="dark"] .category-header {
  border-bottom-color: rgba(255, 255, 255, 0.1);
}

/* Dark mode for feature headers in category analysis */
[data-theme="dark"] .feature-header h2 {
  color: #e2e8f0;
}

[data-theme="dark"] .feature-header .feature-icon {
  color: var(--primary-color);
}

/* Custom Legend for Category Charts */
.chart-legend {
  margin-top: 15px;
  padding: 15px;
  background: linear-gradient(135deg, #f8f9fa, #ffffff);
  border-radius: 10px;
  border: 1px solid #e9ecef;
}

.chart-legend-title {
  font-size: 0.9rem;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 10px;
  text-align: center;
}

.chart-legend-items {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 8px;
}

.chart-legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 10px;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 6px;
  font-size: 0.8rem;
  transition: all 0.2s ease;
}

.chart-legend-item:hover {
  background: rgba(102, 126, 234, 0.1);
  transform: translateX(3px);
}

.chart-legend-color {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  flex-shrink: 0;
}

.chart-legend-text {
  color: #495057;
  font-weight: 500;
}

/* Loan Chart Specific Styles */
.chart-card:has(#dashboardLoanAnalysisChart) {
  background: linear-gradient(135deg, rgba(52, 152, 219, 0.05), rgba(46, 204, 113, 0.05));
  border-left: 4px solid var(--primary-color);
}

.chart-card:has(#dashboardLoanStatusChart) {
  background: linear-gradient(135deg, rgba(243, 156, 18, 0.05), rgba(231, 76, 60, 0.05));
  border-left: 4px solid #f39c12;
}

.chart-card:has(#dashboardLoanAnalysisChart) .chart-header h3,
.chart-card:has(#dashboardLoanStatusChart) .chart-header h3 {
  color: var(--text-color);
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.chart-card:has(#dashboardLoanAnalysisChart) .chart-header h3::before {
  content: "💰";
  font-size: 1.2em;
}

.chart-card:has(#dashboardLoanStatusChart) .chart-header h3::before {
  content: "📊";
  font-size: 1.2em;
}

/* Form Styles */
.form-container {
  background: var(--light-color);
  padding: 30px;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  margin-bottom: 30px;
  animation: slideInLeft 0.5s ease;
}

.form-container h2 {
  margin-bottom: 25px;
  color: var(--primary-color);
  font-weight: 600;
}

.transaction-form {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  align-items: end;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  margin-bottom: 8px;
  font-weight: 400;
  font-size: 1.1rem; /* Added larger font size */
  color: var(--text-color);
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: 12px !important;
  border: 2px solid var(--border-color) !important;
  border-radius: var(--border-radius) !important;
  font-size: 1.1rem !important; /* Increased from 1rem */
  transition: var(--transition) !important;
  background: var(--bg-color) !important;
  color: var(--text-color) !important;
  font-family: var(--bengali-font) !important;
  width: 100% !important;
  box-sizing: border-box !important;
  pointer-events: auto !important;
  user-select: text !important;
  -webkit-user-select: text !important;
  -moz-user-select: text !important;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

/* Button Styles */
.btn {
  padding: 12px 24px;
  border: none;
  border-radius: var(--border-radius);
  cursor: pointer;
  font-size: 1.2rem; /* Increased from 1rem */
  font-weight: 400;
  transition: var(--transition);
  display: inline-flex;
  align-items: center;
  gap: 8px;
  text-decoration: none;
  justify-content: center;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn-primary {
  background: var(--primary-color);
  color: white;
}

.btn-secondary {
  background: var(--border-color);
  color: var(--text-color);
}

.btn-danger {
  background: var(--danger-color);
  color: white;
}

.btn-danger-dark {
  background: linear-gradient(135deg, #d32f2f, #b71c1c);
  color: white;
  border: none;
  font-weight: 600;
  transition: all 0.3s ease;
}

.btn-danger-dark:hover {
  background: linear-gradient(135deg, #b71c1c, #8e0000);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(183, 28, 28, 0.3);
}

.btn-warning {
  background: var(--warning-color);
  color: white;
}

.btn-info {
  background: var(--info-color);
  color: white;
}

/* Transaction History */
.transaction-history {
  background: var(--light-color);
  padding: 30px;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  animation: slideInRight 0.5s ease;
}

.transaction-history h2 {
  margin-bottom: 25px;
  color: var(--primary-color);
  font-weight: 600;
}

/* History Header with Export Button */
.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  padding-bottom: 15px;
  border-bottom: 2px solid var(--border-color);
}

.history-header h2 {
  margin: 0;
  color: var(--primary-color);
  font-weight: 600;
}

.history-controls {
  display: flex;
  align-items: center;
  gap: 15px;
}

.filter-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.history-controls .print-btn {
  background: var(--primary-color);
  border: none;
  color: white;
  padding: 8px 12px;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 0.9rem;
}

.history-controls .print-btn:hover {
  background: var(--primary-hover);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3);
}

.history-controls .print-btn i {
  font-size: 1rem;
}



.transaction-list {
  max-height: 1200px;
  overflow-y: auto;
}

.transaction-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 12px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  margin-bottom: 8px;
  background: var(--bg-color);
  transition: var(--transition);
  position: relative;
  cursor: pointer;
}

.transaction-item:hover {
  transform: translateX(5px);
  box-shadow: var(--shadow);
}

.transaction-item::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 4px;
  height: 100%;
  border-radius: 0 4px 4px 0;
}

.transaction-item.income::before {
  background: var(--secondary-color);
}

.transaction-item.expense::before {
  background: var(--danger-color);
}

.transaction-item.loan-given::before {
  background: var(--warning-color);
}

.transaction-item.loan-taken::before {
  background: var(--info-color);
}

.transaction-item.bank-deposit::before {
  background: var(--secondary-color);
}

.transaction-item.bank-withdraw::before {
  background: var(--danger-color);
}

.transaction-info {
  flex: 1;
  margin-left: 12px;
}

.transaction-info h4 {
  margin-bottom: 3px;
  font-size: 1.3rem; /* Reduced from 1.5rem but kept readable */
  font-family: var(--bengali-font);
  font-weight: 400;
  line-height: 1.2;
}

.transaction-info p {
  font-size: 1rem; /* Reduced from 1.1rem but kept readable */
  opacity: 0.7;
  margin: 1px 0;
  font-family: var(--bengali-font);
  line-height: 1.3;
}

.transaction-amount {
  font-size: 1.8rem; /* Reduced from 2rem */
  font-weight: 700;
  margin-right: 12px;
  margin-top: 15px; /* Reduced space for category badge */
}

.transaction-amount.income {
  color: var(--secondary-color);
}

.transaction-amount.expense {
  color: var(--danger-color);
}

.transaction-amount.loan-given {
  color: var(--warning-color);
}

.transaction-amount.loan-taken {
  color: var(--info-color);
}

.transaction-actions {
  display: flex;
  gap: 10px;
}

.action-btn {
  padding: 8px;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  transition: var(--transition);
  font-size: 0.9rem;
}

.edit-btn {
  background: var(--primary-color);
  color: white;
}

.delete-btn {
  background: var(--danger-color);
  color: white;
}

.action-btn:hover {
  transform: scale(1.1);
}

/* Loan Summary Grid */
.loan-summary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.loan-summary-grid .stat-card {
  padding: 20px;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  background: var(--bg-color);
  border: 1px solid var(--border-color);
  transition: var(--transition);
  position: relative;
  overflow: hidden;
}

.loan-summary-grid .stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.loan-balance-card .stat-icon {
  background: linear-gradient(135deg, #9b59b6, #8e44ad);
}

.loan-status-card .stat-icon {
  background: linear-gradient(135deg, #34495e, #2c3e50);
}

.stat-detail {
  display: block;
  font-size: 1rem;
  opacity: 0.7;
  margin-top: 5px;
  color: var(--text-color);
}

/* Loan Balance Card Colors */
.loan-balance-card.positive-balance .stat-icon {
  background: linear-gradient(135deg, #27ae60, #2ecc71) !important;
}

.loan-balance-card.negative-balance .stat-icon {
  background: linear-gradient(135deg, #e74c3c, #c0392b) !important;
}

.loan-balance-card.positive-balance .stat-amount {
  color: var(--secondary-color);
}

.loan-balance-card.negative-balance .stat-amount {
  color: var(--danger-color);
}

/* Loan Tabs */
.loan-tabs {
  display: flex;
  background: var(--light-color);
  border-radius: var(--border-radius);
  padding: 5px;
  margin-bottom: 20px;
  box-shadow: var(--shadow);
}

.loan-tab {
  flex: 1;
  background: transparent;
  border: none;
  padding: 15px 20px;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  font-weight: 500;
  color: var(--text-color);
}

.loan-tab:hover {
  background: rgba(52, 152, 219, 0.1);
}

.loan-tab.active {
  background: var(--primary-color);
  color: white;
  box-shadow: var(--shadow);
  font-size: 1.3rem;
}

.loan-content {
  display: none;
}

.loan-content.active {
  display: block;
  animation: fadeInUp 0.3s ease;
}

/* Bank Tabs */
.bank-tabs {
  display: flex;
  background: var(--light-color);
  border-radius: var(--border-radius);
  padding: 5px;
  margin-bottom: 20px;
  box-shadow: var(--shadow);
}

.bank-tab {
  flex: 1;
  background: transparent;
  border: none;
  padding: 15px 20px;
  border-radius: var(--border-radius);
  cursor: pointer;
  font-weight: 500;
  color: var(--text-color);
  transition: var(--transition);
  font-size: 1.3rem;
}

.bank-tab:hover {
  background: rgba(52, 152, 219, 0.1);
  color: var(--primary-color);
}

.bank-tab.active {
  background: var(--primary-color);
  color: white;
  box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3);
}

.bank-content {
  display: none;
}

.bank-content.active {
  display: block;
  animation: fadeInUp 0.3s ease;
}

/* Reports */
.report-filters {
  background: var(--light-color);
  padding: 30px;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  margin-bottom: 30px;
  animation: slideInDown 0.5s ease;
}

.report-filters h2 {
  margin-bottom: 25px;
  color: var(--primary-color);
  font-weight: 600;
}

.filter-group {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.report-summary {
  background: var(--light-color);
  padding: 30px;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  margin-bottom: 30px;
  animation: fadeIn 0.5s ease;
}

.summary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.summary-item {
  text-align: center;
  padding: 20px;
  background: var(--bg-color);
  border-radius: var(--border-radius);
  border: 2px solid var(--border-color);
  transition: var(--transition);
}

.summary-item:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow);
}

.summary-item h3 {
  font-size: 0.9rem;
  margin-bottom: 10px;
  opacity: 0.8;
}

.summary-item .amount {
  font-size: 1.5rem;
  font-weight: 700;
}

/* Charts */
.report-charts {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 30px;
}

.chart-container {
  background: var(--light-color);
  padding: 30px;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  animation: slideInUp 0.5s ease;
}

/* Transaction Details Modal Styles */
.transaction-details {
  padding: 20px 0;
}

.transaction-details .detail-item {
  margin-bottom: 15px;
  padding: 10px;
  background: rgba(var(--primary-color-rgb), 0.05);
  border-radius: var(--border-radius);
  border-left: 4px solid var(--primary-color);
}

.transaction-details .detail-item p {
  margin: 5px 0;
  font-family: var(--bengali-font);
  line-height: 1.5;
}

.transaction-details .detail-item strong {
  color: var(--text-color);
  font-weight: 600;
}

.modal-actions {
  display: flex;
  gap: 10px;
  justify-content: center;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid var(--border-color);
}

.modal-actions .btn {
  min-width: 120px;
  padding: 10px 15px;
  font-family: var(--bengali-font);
  font-weight: 500;
}

/* Modal Styles */
.modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  animation: fadeIn 0.3s ease;
  cursor: default;
}

.modal[style*="display: block"] {
  display: flex !important;
  align-items: center;
  justify-content: center;
}

.modal.show {
  display: flex;
}

.modal.active {
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-content {
  background: var(--bg-color);
  border-radius: var(--border-radius);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  animation: slideInDown 0.3s ease;
  margin-left: auto;
  margin-right: auto;
  margin-top: auto;
  margin-bottom: auto;
  cursor: pointer;
  position: relative;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 70px 20px 30px;
  border-bottom: 1px solid var(--border-color);
  position: relative;
}

.modal-header h3 {
  margin: 0;
  color: var(--primary-color);
  flex: 1;
  padding-right: 20px;
}

/* Modal Controls */
.modal-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.modal-fullscreen,
.color-picker-fullscreen,
.image-modal-fullscreen {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: var(--text-color);
  width: 35px;
  height: 35px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  font-size: 14px;
}

.modal-fullscreen:hover,
.color-picker-fullscreen:hover,
.image-modal-fullscreen:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

/* Fullscreen Modal Styles */
.modal.fullscreen {
  z-index: 9999;
}

.modal.fullscreen .modal-content {
  width: 100% !important;
  height: 100% !important;
  max-width: none !important;
  max-height: none !important;
  border-radius: 0 !important;
  margin: 0 !important;
  animation: none !important;
}

.color-picker-modal.fullscreen {
  z-index: 9999;
}

.color-picker-modal.fullscreen .color-picker-content {
  width: 100% !important;
  height: 100% !important;
  max-width: none !important;
  max-height: none !important;
  border-radius: 0 !important;
  margin: 0 !important;
  animation: none !important;
}

.image-modal.fullscreen {
  z-index: 9999;
}

.image-modal.fullscreen .image-modal-content {
  max-width: 100% !important;
  max-height: 100% !important;
  width: auto !important;
  height: auto !important;
  border-radius: 0 !important;
}

.close-modal,
.modal-close {
  background: #e74c3c !important;
  border: 3px solid #c0392b !important;
  border-radius: 50% !important;
  width: 45px !important;
  height: 45px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 1.4rem !important;
  cursor: pointer !important;
  color: white !important;
  transition: all 0.3s ease !important;
  position: absolute !important;
  top: 15px !important;
  right: 15px !important;
  z-index: 10000 !important;
  pointer-events: auto !important;
  box-shadow: 0 2px 8px rgba(231, 76, 60, 0.4) !important;
  outline: none !important;
  user-select: none !important;
}

.close-modal:hover,
.modal-close:hover {
  background: #c0392b !important;
  color: white !important;
  transform: scale(1.15) !important;
  border-color: #a93226 !important;
  box-shadow: 0 6px 20px rgba(231, 76, 60, 0.6) !important;
  cursor: pointer !important;
}

.close-modal:active,
.modal-close:active {
  transform: scale(1.05) !important;
  box-shadow: 0 2px 8px rgba(231, 76, 60, 0.8) !important;
}

.close-modal i,
.modal-close i {
  pointer-events: none !important;
  font-size: 1.2rem !important;
  font-weight: bold !important;
  color: white !important;
}

/* Additional cursor enforcement */
.close-modal *,
.modal-close * {
  cursor: pointer !important;
}

/* Specific targeting for FontAwesome icons */
.close-modal .fa,
.modal-close .fa,
.close-modal .fas,
.modal-close .fas {
  cursor: pointer !important;
  pointer-events: none !important;
}

/* Dark Mode Support for Modal Close Button */
[data-theme="dark"] .close-modal,
[data-theme="dark"] .modal-close {
  background: #e74c3c !important;
  border-color: #c0392b !important;
  color: white !important;
  cursor: pointer !important;
}

[data-theme="dark"] .close-modal:hover,
[data-theme="dark"] .modal-close:hover {
  background: #c0392b !important;
  color: white !important;
  border-color: #a93226 !important;
  cursor: pointer !important;
  transform: scale(1.15) !important;
  box-shadow: 0 6px 20px rgba(231, 76, 60, 0.6) !important;
}

/* Global cursor enforcement for all close buttons */
button.close-modal,
button.modal-close,
.close-modal,
.modal-close {
  cursor: pointer !important;
}

button.close-modal:hover,
button.modal-close:hover,
.close-modal:hover,
.modal-close:hover {
  cursor: pointer !important;
}

/* Override any conflicting cursor styles */
.modal .close-modal,
.modal .modal-close {
  cursor: pointer !important;
}

.modal .close-modal:hover,
.modal .modal-close:hover {
  cursor: pointer !important;
}

/* Ultra-specific targeting for transaction details modal */
#transactionDetailsModal .close-modal,
#transactionDetailsModal .modal-close {
  background: #e74c3c !important;
  border: 3px solid #c0392b !important;
  border-radius: 50% !important;
  width: 50px !important;
  height: 50px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 1.5rem !important;
  cursor: pointer !important;
  color: white !important;
  transition: all 0.3s ease !important;
  position: absolute !important;
  top: 10px !important;
  right: 10px !important;
  z-index: 99999 !important;
  pointer-events: auto !important;
  box-shadow: 0 4px 12px rgba(231, 76, 60, 0.5) !important;
  outline: none !important;
  user-select: none !important;
}

#transactionDetailsModal .close-modal:hover,
#transactionDetailsModal .modal-close:hover {
  background: #c0392b !important;
  color: white !important;
  transform: scale(1.2) !important;
  border-color: #a93226 !important;
  box-shadow: 0 8px 25px rgba(231, 76, 60, 0.7) !important;
  cursor: pointer !important;
}

#transactionDetailsModal .close-modal i,
#transactionDetailsModal .modal-close i {
  pointer-events: none !important;
  font-size: 1.3rem !important;
  font-weight: bold !important;
  color: white !important;
  cursor: pointer !important;
}

/* Force cursor on everything inside close button */
#transactionDetailsModal .close-modal,
#transactionDetailsModal .close-modal *,
#transactionDetailsModal .modal-close,
#transactionDetailsModal .modal-close * {
  cursor: pointer !important;
}

.modal-body {
  padding: 30px;
}

.modal-actions {
  display: flex;
  gap: 15px;
  justify-content: flex-end;
  margin-top: 25px;
}

/* Notification */
.notification {
  position: fixed;
  top: 20px;
  left: 20px;
  background: var(--primary-color);
  color: white;
  padding: 0;
  border-radius: 12px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
  z-index: 1001;
  transform: translateX(-400px) scale(0.9);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  font-family: "Noto Sans Bengali", "Poppins", sans-serif;
  font-weight: 500;
  min-width: 280px;
  max-width: 320px;
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  opacity: 0;
  overflow: hidden;
}

.notification.show {
  transform: translateX(0) scale(1);
  opacity: 1;
  animation: notificationSlideInFromLeft 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.notification.success {
  background: linear-gradient(135deg, var(--secondary-color), #27ae60);
  border-color: rgba(46, 204, 113, 0.3);
}

.notification.success .notification-icon {
  background: linear-gradient(135deg, rgba(46, 204, 113, 0.3), rgba(39, 174, 96, 0.3));
  color: #ffffff;
}

.notification.error {
  background: linear-gradient(135deg, var(--danger-color), #c0392b);
  border-color: rgba(231, 76, 60, 0.3);
}

.notification.error .notification-icon {
  background: linear-gradient(135deg, rgba(231, 76, 60, 0.3), rgba(192, 57, 43, 0.3));
  color: #ffffff;
}

.notification.warning {
  background: linear-gradient(135deg, var(--warning-color), #d68910);
  border-color: rgba(243, 156, 18, 0.3);
}

.notification.warning .notification-icon {
  background: linear-gradient(135deg, rgba(243, 156, 18, 0.3), rgba(214, 137, 16, 0.3));
  color: #ffffff;
}

.notification.info {
  background: linear-gradient(135deg, var(--primary-color), #2980b9);
  border-color: rgba(52, 152, 219, 0.3);
}

.notification.info .notification-icon {
  background: linear-gradient(135deg, rgba(52, 152, 219, 0.3), rgba(41, 128, 185, 0.3));
  color: #ffffff;
}

.notification-content {
  display: flex;
  align-items: stretch;
  gap: 0;
  font-size: 18px;
  line-height: 1.3;
  height: 100%;
  min-height: 50px;
}

.notification-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  min-width: 40px;
  height: 100%;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  font-size: 16px !important;
  animation: notificationIconPulse 2s infinite;
  border-radius: 0 11px 11px 0;
  position: relative;
  text-align: center;
  line-height: 1;
}

.notification-icon::after {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 1px;
  background: rgba(255, 255, 255, 0.2);
}

/* Specific styles for Font Awesome icons in notifications */
.notification-icon.fas,
.notification-icon.fa {
  font-size: 16px !important;
  line-height: 1.5 !important;
  vertical-align: middle;
  margin: 0px auto !important;
}

.notification-icon.fa-info-circle,
.notification-icon.fa-check-circle,
.notification-icon.fa-exclamation-circle,
.notification-icon.fa-exclamation-triangle {
  font-size: 18px !important;
}

.notification-message {
  flex: 1;
  font-weight: 500;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  padding: 15px 18px 15px 20px;
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 11px 0 0 11px;
  position: relative;
  min-height: 50px;
}

.notification-close {
  position: absolute;
  bottom: 8px;
  right: 8px;
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  transition: all 0.3s ease;
  opacity: 0.7;
}

.notification-close:hover {
  background: rgba(255, 255, 255, 0.3);
  opacity: 1;
  transform: scale(1.1);
}

/* Animations */
@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes notificationSlideInFromLeft {
  0% {
    transform: translateX(-500px) scale(0.8) rotateY(-90deg);
    opacity: 0;
  }
  50% {
    transform: translateX(20px) scale(1.05) rotateY(0deg);
    opacity: 0.8;
  }
  100% {
    transform: translateX(0) scale(1) rotateY(0deg);
    opacity: 1;
  }
}

@keyframes notificationIconPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

@keyframes notificationSlideInMobileFromLeft {
  0% {
    transform: translateX(-100%) scale(0.9);
    opacity: 0;
  }
  50% {
    transform: translateX(10px) scale(1.02);
    opacity: 0.8;
  }
  100% {
    transform: translateX(0) scale(1);
    opacity: 1;
  }
}

/* Color Picker Modal Styles */
.color-picker-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 10000;
    backdrop-filter: blur(5px);
}

.color-picker-modal.active {
    display: flex;
    align-items: center;
    justify-content: center;
}

.color-picker-content {
    background: var(--light-color);
    border-radius: var(--border-radius);
    padding: 25px;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease;
}

.color-picker-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--border-color);
}

.color-picker-header h3 {
    color: var(--text-color);
    margin: 0;
    display: flex;
    align-items: center;
    gap: 10px;
    flex: 1;
}

.color-picker-controls {
    display: flex;
    align-items: center;
    gap: 10px;
}

.close-color-picker {
    background: none;
    border: none;
    color: var(--text-color);
    font-size: 18px;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.close-color-picker:hover {
    background: var(--danger-color);
    color: white;
}

.color-options {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.color-section h4 {
    color: var(--text-color);
    margin: 0 0 10px 0;
    font-size: 1rem;
}

.preset-colors {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(50px, 1fr));
    gap: 10px;
}

.color-option {
    width: 50px;
    height: 50px;
    border: 3px solid transparent;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.color-option:hover {
    border-color: var(--text-color);
    transform: scale(1.1);
}

.color-option.active {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px var(--primary-color);
}

.custom-color-picker {
    display: flex;
    align-items: center;
    gap: 15px;
}

.custom-color-picker input[type="color"] {
    width: 60px;
    height: 40px;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
}

/* Background Color Options */
.background-colors {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(60px, 1fr));
    gap: 10px;
    margin-top: 10px;
}

.background-option {
    width: 60px;
    height: 60px;
    border: 2px solid #ddd;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    color: #666;
    font-weight: bold;
}

.background-option:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.background-option.active {
    border: 3px solid var(--primary-color);
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.3);
}

/* Background Pattern Options */
.background-patterns {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
    gap: 15px;
    margin-top: 10px;
}

.pattern-option {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    padding: 10px;
    border: 2px solid #ddd;
    border-radius: 8px;
    background: #fff;
    cursor: pointer;
    transition: all 0.3s ease;
}

.pattern-option:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.pattern-option.active {
    border: 3px solid var(--primary-color);
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.3);
}

.pattern-preview {
    width: 40px;
    height: 40px;
    border-radius: 4px;
    border: 1px solid #eee;
}

.pattern-option span {
    font-size: 12px;
    color: #666;
    font-weight: 500;
}

/* Pattern Styles */
.pattern-dots {
    background-image: radial-gradient(circle, #ccc 1px, transparent 1px);
    background-size: 8px 8px;
    background-color: #f8f9fa;
}

.pattern-grid {
    background-image:
        linear-gradient(#ccc 1px, transparent 1px),
        linear-gradient(90deg, #ccc 1px, transparent 1px);
    background-size: 10px 10px;
    background-color: #f8f9fa;
}

.pattern-diagonal {
    background-image: repeating-linear-gradient(
        45deg,
        transparent,
        transparent 5px,
        #ccc 5px,
        #ccc 6px
    );
    background-color: #f8f9fa;
}

.pattern-waves {
    background-image:
        radial-gradient(ellipse at top, transparent 60%, #ccc 61%, #ccc 62%, transparent 63%);
    background-size: 20px 10px;
    background-color: #f8f9fa;
}

.pattern-hexagon {
    background-image:
        radial-gradient(circle at 25% 25%, transparent 20%, #ccc 21%, #ccc 22%, transparent 23%),
        radial-gradient(circle at 75% 75%, transparent 20%, #ccc 21%, #ccc 22%, transparent 23%);
    background-size: 12px 12px;
    background-color: #f8f9fa;
}

.pattern-stripes {
    background-image: repeating-linear-gradient(
        0deg,
        transparent,
        transparent 4px,
        #ccc 4px,
        #ccc 8px
    );
    background-color: #f8f9fa;
}

.pattern-zigzag {
    background-image: linear-gradient(
        135deg,
        #ccc 25%,
        transparent 25%,
        transparent 50%,
        #ccc 50%,
        #ccc 75%,
        transparent 75%
    );
    background-size: 10px 10px;
    background-color: #f8f9fa;
}

.pattern-circles {
    background-image: radial-gradient(circle, #ccc 1px, transparent 1px);
    background-size: 12px 12px;
    background-color: #f8f9fa;
}

.pattern-triangles {
    background-image:
        linear-gradient(60deg, #ccc 25%, transparent 25%),
        linear-gradient(-60deg, #ccc 25%, transparent 25%);
    background-size: 15px 15px;
    background-position: 0 0, 7px 0;
    background-color: #f8f9fa;
}

.pattern-crosshatch {
    background-image:
        linear-gradient(45deg, #ccc 25%, transparent 25%),
        linear-gradient(-45deg, #ccc 25%, transparent 25%);
    background-size: 8px 8px;
    background-color: #f8f9fa;
}

.pattern-stars {
    background-image:
        radial-gradient(circle at 50% 50%, #ccc 1px, transparent 1px),
        radial-gradient(circle at 25% 25%, #ccc 0.5px, transparent 0.5px),
        radial-gradient(circle at 75% 75%, #ccc 0.5px, transparent 0.5px);
    background-size: 20px 20px, 10px 10px, 15px 15px;
    background-position: 0 0, 5px 5px, 10px 10px;
    background-color: #f8f9fa;
}

/* Custom Background Picker */
.custom-background-picker {
    display: flex;
    gap: 10px;
    align-items: center;
    margin-top: 10px;
}

.custom-background-picker input[type="color"] {
    width: 50px;
    height: 40px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
}

/* Body Pattern Classes */
body.pattern-dots {
    background-image: radial-gradient(circle, rgba(0, 0, 0, 0.1) 1px, transparent 1px);
    background-size: 20px 20px;
}

body.pattern-grid {
    background-image:
        linear-gradient(rgba(0, 0, 0, 0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(0, 0, 0, 0.1) 1px, transparent 1px);
    background-size: 20px 20px;
}

body.pattern-diagonal {
    background-image: repeating-linear-gradient(
        45deg,
        transparent,
        transparent 10px,
        rgba(0, 0, 0, 0.05) 10px,
        rgba(0, 0, 0, 0.05) 12px
    );
}

body.pattern-waves {
    background-image:
        radial-gradient(ellipse at top, transparent 60%, rgba(0, 0, 0, 0.05) 61%, rgba(0, 0, 0, 0.05) 62%, transparent 63%);
    background-size: 40px 20px;
}

body.pattern-hexagon {
    background-image:
        radial-gradient(circle at 25% 25%, transparent 20%, rgba(0, 0, 0, 0.05) 21%, rgba(0, 0, 0, 0.05) 22%, transparent 23%),
        radial-gradient(circle at 75% 75%, transparent 20%, rgba(0, 0, 0, 0.05) 21%, rgba(0, 0, 0, 0.05) 22%, transparent 23%);
    background-size: 24px 24px;
}

body.pattern-stripes {
    background-image: repeating-linear-gradient(
        0deg,
        transparent,
        transparent 8px,
        rgba(0, 0, 0, 0.05) 8px,
        rgba(0, 0, 0, 0.05) 16px
    );
}

body.pattern-zigzag {
    background-image: linear-gradient(
        135deg,
        rgba(0, 0, 0, 0.05) 25%,
        transparent 25%,
        transparent 50%,
        rgba(0, 0, 0, 0.05) 50%,
        rgba(0, 0, 0, 0.05) 75%,
        transparent 75%
    );
    background-size: 20px 20px;
}

body.pattern-circles {
    background-image: radial-gradient(circle, rgba(0, 0, 0, 0.05) 2px, transparent 2px);
    background-size: 25px 25px;
}

body.pattern-triangles {
    background-image:
        linear-gradient(60deg, rgba(0, 0, 0, 0.05) 25%, transparent 25%),
        linear-gradient(-60deg, rgba(0, 0, 0, 0.05) 25%, transparent 25%);
    background-size: 30px 30px;
    background-position: 0 0, 15px 0;
}

body.pattern-crosshatch {
    background-image:
        linear-gradient(45deg, rgba(0, 0, 0, 0.05) 25%, transparent 25%),
        linear-gradient(-45deg, rgba(0, 0, 0, 0.05) 25%, transparent 25%);
    background-size: 15px 15px;
}

body.pattern-stars {
    background-image:
        radial-gradient(circle at 50% 50%, rgba(0, 0, 0, 0.05) 1px, transparent 1px),
        radial-gradient(circle at 25% 25%, rgba(0, 0, 0, 0.03) 1px, transparent 1px),
        radial-gradient(circle at 75% 75%, rgba(0, 0, 0, 0.03) 1px, transparent 1px);
    background-size: 40px 40px, 20px 20px, 30px 30px;
    background-position: 0 0, 10px 10px, 20px 20px;
}

/* Header Color Button */
.header-color-btn {
    background: rgba(255, 255, 255, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    font-size: 18px;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: all 0.3s ease;
    margin-right: 0px;
}

.header-color-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: scale(1.1);
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 10px;
  }

  .header {
    height: 65px; /* Increased mobile header height */
    padding: 0 15px;
    position: sticky;
    top: 0;
    z-index: 1000;
  }

  .header-content {
    flex-direction: row; /* Keep single line on mobile */
    justify-content: space-between;
    gap: 10px;
  }

  .header-center {
    display: none; /* Hide center section on mobile */
  }

  .header-left .current-datetime {
    font-size: 0.65rem; /* Even smaller on mobile */
    opacity: 0.7;
  }

  .header h1 {
    font-size: 1.2rem; /* Increased mobile header font */
  }

  .nav-tabs {
    flex-direction: column;
    gap: 5px;
    top: 85px; /* Adjust for larger mobile header with gap */
  }

  .nav-tab {
    justify-content: flex-start;
    padding: 12px 15px;
  }

  .dashboard-grid {
    grid-template-columns: 1fr;
    gap: 15px;
    padding: 15px;
    border-radius: 15px 15px 0 0;
    width: 100%;
    max-width: 100%;
    left: 0;
    transform: none;
  }

  .dashboard-grid.hidden {
    transform: translateY(100%);
  }

  .floating-dashboard-toggle {
    width: 50px;
    height: 50px;
    font-size: 1rem;
    bottom: 15px;
    right: 15px;
  }

  .stat-card {
    padding: 18px;
    min-height: 80px;
    gap: 15px;
  }

  .stat-icon {
    width: 50px;
    height: 50px;
    font-size: 1.2rem;
  }

  .stat-info h3 {
    font-size: 0.9rem;
  }

  .stat-amount {
    font-size: 1.4rem;
  }

  .transaction-form {
    grid-template-columns: 1fr;
  }

  .transaction-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .transaction-actions {
    align-self: flex-end;
  }

  .loan-summary-grid {
    grid-template-columns: 1fr;
    gap: 15px;
    margin-bottom: 20px;
  }

  .loan-summary-grid .stat-card {
    padding: 15px;
  }

  .report-charts {
    grid-template-columns: 1fr;
  }

  .chart-container {
    padding: 20px;
  }

  .modal-content {
    width: 95%;
    margin: 20px;
  }

  .modal-body {
    padding: 20px;
  }

  .filter-group {
    grid-template-columns: 1fr;
  }

  .summary-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .header {
    padding: 8px 15px; /* Increased padding for very small screens */
    position: sticky;
    top: 0;
    z-index: 1000;
  }

  .header h1 {
    font-size: 1.1rem; /* Increased font for very small screens */
  }

  .nav-tabs {
    top: 75px; /* Adjust for larger header on very small screens */
  }

  .balance-container {
    gap: 8px;
  }

  .balance-toggle {
    width: 35px;
    height: 35px;
    font-size: 0.9rem;
  }

  .current-balance {
    font-size: 1rem;
    padding: 12px 20px;
    border-radius: 12px;
  }

  .balance-amount {
    font-size: 1.5rem;
    font-weight: 700;
  }

  /* Income stats responsive */
  .income-stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    margin-bottom: 20px;
    padding: 15px 0;
  }

  .income-stat-card {
    padding: 15px;
    gap: 12px;
    min-height: 70px;
    flex-direction: column;
    text-align: center;
  }

  .income-stat-card .stat-icon {
    width: 40px;
    height: 40px;
    font-size: 18px;
    margin: 0 auto;
  }

  .income-stat-card .stat-info {
    text-align: center;
  }

  .income-stat-card .stat-info h3 {
    font-size: 0.9rem;
    margin-bottom: 5px;
  }

  .income-stat-card .stat-amount {
    font-size: 1.3rem;
  }

  /* Expense stats responsive */
  .expense-stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    margin-bottom: 20px;
    padding: 15px 0;
  }

  .expense-stat-card {
    padding: 15px;
    gap: 12px;
    min-height: 70px;
    flex-direction: column;
    text-align: center;
  }

  .expense-stat-card .stat-icon {
    width: 40px;
    height: 40px;
    font-size: 18px;
    margin: 0 auto;
  }

  .expense-stat-card .stat-info {
    text-align: center;
  }

  .expense-stat-card .stat-info h3 {
    font-size: 0.9rem;
    margin-bottom: 5px;
  }

  .expense-stat-card .stat-amount {
    font-size: 1.3rem;
  }

  /* Loan stats responsive */
  .loan-stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    margin-bottom: 20px;
    padding: 15px 0;
  }

  .loan-stat-card {
    padding: 15px;
    gap: 12px;
    min-height: 70px;
    flex-direction: column;
    text-align: center;
  }

  .loan-stat-card .stat-icon {
    width: 40px;
    height: 40px;
    font-size: 18px;
    margin: 0 auto;
  }

  .loan-stat-card .stat-info {
    text-align: center;
  }

  .loan-stat-card .stat-info h3 {
    font-size: 0.9rem;
    margin-bottom: 5px;
  }

  .loan-stat-card .stat-amount {
    font-size: 1.3rem;
  }

  /* Bank stats responsive */
  .bank-stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    margin-bottom: 20px;
    padding: 15px 0;
  }

  .bank-stat-card {
    padding: 15px;
    gap: 12px;
    min-height: 70px;
    flex-direction: column;
    text-align: center;
  }

  .bank-stat-card .stat-icon {
    width: 40px;
    height: 40px;
    font-size: 18px;
    margin: 0 auto;
  }

  .bank-stat-card .stat-info {
    text-align: center;
  }

  .bank-stat-card .stat-info h3 {
    font-size: 0.9rem;
    margin-bottom: 5px;
  }

  .bank-stat-card .stat-amount {
    font-size: 1.3rem;
  }

  .stat-card {
    padding: 15px;
    gap: 15px;
  }

  .stat-icon {
    width: 50px;
    height: 50px;
    font-size: 1.2rem;
  }

  .stat-amount {
    font-size: 1.4rem;
  }

  .form-container,
  .transaction-history,
  .report-filters,
  .report-summary,
  .chart-container {
    padding: 20px;
  }

  .summary-grid {
    grid-template-columns: 1fr;
  }

  .notification {
    left: 10px;
    right: 10px;
    top: 15px;
    min-width: auto;
    max-width: none;
    transform: translateX(-100%) scale(0.95);
    border-radius: 10px;
  }

  .notification.show {
    transform: translateX(0) scale(1);
    animation: notificationSlideInMobileFromLeft 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  }

  .notification-content {
    font-size: 13px;
    min-height: 45px;
  }

  .notification-icon {
    width: 35px;
    min-width: 35px;
    font-size: 14px !important;
    border-radius: 0 9px 9px 0;
    height: 100%;
  }

  .notification-icon.fas,
  .notification-icon.fa {
    font-size: 14px !important;
  }

  .notification-icon.fa-info-circle,
  .notification-icon.fa-check-circle,
  .notification-icon.fa-exclamation-circle,
  .notification-icon.fa-exclamation-triangle {
    font-size: 16px !important;
  }

  .notification-message {
    padding: 12px 15px;
    font-size: 13px;
    border-radius: 9px 0 0 9px;
    min-height: 45px;
  }

  .notification-close {
    width: 18px;
    height: 18px;
    font-size: 9px;
    bottom: 6px;
    right: 6px;
  }
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--light-color);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary-color);
}

/* Loading Animation */
.loading {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #fff;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: var(--border-color);
}

.empty-state i {
  font-size: 3rem;
  margin-bottom: 15px;
  opacity: 0.5;
}

.empty-state h3 {
  margin-bottom: 10px;
  font-weight: 500;
}

.empty-state p {
  opacity: 0.7;
}

/* Status Indicators */
.status-indicator {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: uppercase;
}

.status-pending {
  background: rgba(241, 196, 15, 0.2);
  color: var(--warning-color);
}

.status-paid {
  background: rgba(46, 204, 113, 0.2);
  color: var(--secondary-color);
}

.status-overdue {
  background: rgba(231, 76, 60, 0.2);
  color: var(--danger-color);
}

/* Paid Loan Styles */
.transaction-item.loan-paid {
  opacity: 0.7;
  background: rgba(46, 204, 113, 0.05);
  border-color: rgba(46, 204, 113, 0.3);
}

.transaction-item.loan-paid::before {
  background: var(--secondary-color);
  opacity: 0.6;
}

.transaction-item.loan-paid .transaction-info h4 {
  text-decoration: line-through;
  opacity: 0.8;
}

.transaction-amount.amount-paid {
  color: var(--secondary-color) !important;
  opacity: 0.8;
}

.transaction-amount.amount-paid i {
  margin-right: 5px;
}

/* Action Button Styles */
.action-btn.btn-primary {
  background: var(--primary-color);
  color: white;
}

.action-btn.btn-success {
  background: var(--secondary-color);
  color: white;
}

.action-btn.btn-primary:hover,
.action-btn.btn-success:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* Mobile Responsive Styles for Notification */
@media (max-width: 768px) {
  .notification-panel {
    width: 100vw;
    right: -100vw;
  }

  .notification-panel.show {
    right: 0;
  }

  .notification-bell {
    width: 40px;
    height: 40px;
    padding: 8px;
  }

  .notification-bell i {
    font-size: 16px;
  }

  .notification-badge {
    width: 18px;
    height: 18px;
    font-size: 9px;
    top: -3px;
    right: -3px;
  }

  .header-controls {
    gap: 8px;
  }

  .global-search-btn {
    padding: 6px 10px;
    font-size: 12px;
  }

  .global-search-btn span {
    display: none;
  }

  .fullscreen-btn {
    width: 32px;
    height: 32px;
    font-size: 14px;
  }

  .notification-item-time {
    font-size: 10px;
  }

  .notification-item-time .time-ago {
    font-size: 9px;
  }

  .notification-item-time .full-datetime {
    font-size: 8px;
  }

  .notification-item-title {
    font-size: 13px;
  }

  .notification-item-content {
    font-size: 12px;
  }
}

/* Print Styles */
@media print {
  .header-controls,
  .nav-tabs,
  .transaction-actions,
  .btn,
  .modal,
  .notification,
  .notification-panel {
    display: none !important;
  }

  .container {
    max-width: none;
    padding: 0;
  }

  .stat-card,
  .form-container,
  .transaction-history,
  .report-summary {
    box-shadow: none;
    border: 1px solid #ddd;
    break-inside: avoid;
  }
}

/* Settings Section Styles */
.settings-section {
  max-width: 800px;
  margin: 0 auto;
}

/* Storage Location Form Styles */
.storage-location-form {
    padding: 20px 0;
}

.path-input-group {
    display: flex;
    gap: 10px;
    align-items: center;
    margin-bottom: 10px;
}

.path-input-group input {
    flex: 1;
    padding: 10px 15px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
}

.path-input-group input:focus {
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
    outline: none;
}

.path-input-group .btn {
    padding: 10px 15px;
    border-radius: 8px;
    font-size: 14px;
    min-width: 45px;
}

.current-path-display {
    margin: 20px 0;
    padding: 15px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 10px;
    border: 1px solid #dee2e6;
}

.current-path-display label {
    display: block;
    margin-bottom: 10px;
    font-weight: 600;
    color: #495057;
}

.path-display-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 15px;
    background: white;
    border-radius: 6px;
    border: 1px solid #ced4da;
}

.path-display-box span {
    font-family: 'Courier New', monospace;
    color: #6c757d;
    font-size: 13px;
    word-break: break-all;
}

.storage-actions {
    display: flex;
    gap: 15px;
    margin-top: 20px;
    flex-wrap: wrap;
}

.storage-actions .btn {
    padding: 10px 20px;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.storage-actions .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Dark mode support for storage form */
[data-theme="dark"] .path-input-group input {
    background: #2c3e50;
    border-color: #34495e;
    color: #ecf0f1;
}

[data-theme="dark"] .path-input-group input:focus {
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
}

[data-theme="dark"] .current-path-display {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    border-color: #4a5568;
}

[data-theme="dark"] .current-path-display label {
    color: #e2e8f0;
}

[data-theme="dark"] .path-display-box {
    background: #1a202c;
    border-color: #4a5568;
}

[data-theme="dark"] .path-display-box span {
    color: #a0aec0;
}





.settings-card {
  background: var(--light-color);
  border-radius: var(--border-radius);
  padding: 25px;
  margin-bottom: 25px;
  box-shadow: var(--shadow);
  border: 1px solid var(--border-color);
  transition: var(--transition);
}

/* Data Stats Container Specific Styling */
.data-stats {
  background: transparent;
  padding: 0;
}

/* Light Mode Data Stats Overrides */
.data-stats .stats-section {
  background: #7e7e81  !important;
  border: 2px solid #e8f4fd !important;
  box-shadow: 0 4px 20px rgba(52, 152, 219, 0.1) !important;
  margin-bottom: 25px;
}

.data-stats .stats-section-title {
  color: #2c3e50 !important;
  font-weight: 700 !important;
}

.data-stats .stat-item {
  background: #ffffff !important;
  border: 2px solid #e1e8ed !important;
  color: #2c3e50 !important;
}

.data-stats .stat-item h4,
.data-stats .stat-item h5 {
  color: #2c3e50 !important;
  font-weight: 600 !important;
}

.data-stats .stat-item .stat-number,
.data-stats .stat-item .stat-value {
  color: var(--primary-color) !important;
  font-weight: 700 !important;
}

.data-stats .stat-item .stat-detail {
  color: #5a6c7d !important;
  font-weight: 500 !important;
}

.data-stats .category-group {
  background: #ffffff !important;
  border: 2px solid #e1e8ed !important;
}

.data-stats .category-group h5 {
  color: #2c3e50 !important;
  font-weight: 600 !important;
}

.data-stats .category-item {
  color: #2c3e50 !important;
}

.data-stats .category-amount {
  color: var(--primary-color) !important;
  font-weight: 600 !important;
}

/* Notification Permission Settings Styles */
.notification-permission-settings {
  border: 2px solid #e3f2fd;
  border-radius: 12px;
  padding: 20px;
  margin: 20px 0;
  background: linear-gradient(135deg, #f3f9ff 0%, #e3f2fd 100%);
}

.notification-permission-settings > label {
  font-weight: 600;
  color: #1976d2;
  font-size: 16px;
  margin-bottom: 15px;
  display: block;
}

.permission-status-group {
  margin: 15px 0;
}

.permission-status {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px 16px;
  background: rgba(25, 118, 210, 0.1);
  border-radius: 8px;
  border: 1px solid rgba(25, 118, 210, 0.2);
}

.btn-sm {
  padding: 4px 8px;
  font-size: 12px;
  border-radius: 4px;
}

.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  display: inline-block;
  animation: pulse 2s infinite;
}

.status-indicator.granted {
  background-color: #4CAF50;
  box-shadow: 0 0 8px rgba(76, 175, 80, 0.4);
}

.status-indicator.denied {
  background-color: #f44336;
  box-shadow: 0 0 8px rgba(244, 67, 54, 0.4);
}

.status-indicator.default {
  background-color: #ff9800;
  box-shadow: 0 0 8px rgba(255, 152, 0, 0.4);
}

.status-text {
  font-weight: 500;
  color: #1976d2;
}

.permission-controls {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  margin: 15px 0;
}

.permission-controls .btn {
  flex: 1;
  min-width: 140px;
  font-size: 14px;
  padding: 10px 15px;
}

.permission-guide {
  background: rgba(25, 118, 210, 0.05);
  border: 1px solid rgba(25, 118, 210, 0.1);
  border-radius: 8px;
  padding: 15px;
  margin-top: 15px;
}

.permission-guide h4 {
  margin: 0 0 10px 0;
  font-size: 14px;
  color: #1976d2;
  font-weight: 600;
}

.permission-guide ul {
  margin: 10px 0;
  padding-left: 20px;
}

.permission-guide li {
  margin: 5px 0;
  font-size: 13px;
  color: #555;
}

.permission-guide li strong {
  color: #1976d2;
}

.permission-guide p {
  margin: 10px 0 0 0;
  font-size: 12px;
  color: #666;
  font-style: italic;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

/* Dark Mode Support for Notification Settings */
.dark-mode .notification-permission-settings {
  background: linear-gradient(135deg, #1a237e 0%, #283593 100%);
  border: 2px solid #3f51b5;
  color: #e8eaf6;
}

.dark-mode .notification-permission-settings > label {
  color: #e8eaf6;
}

.dark-mode .permission-status {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.dark-mode .status-text {
  color: #e8eaf6;
}

.dark-mode .permission-guide {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: #e8eaf6;
}

.dark-mode .permission-guide h4 {
  color: #e8eaf6;
}

.dark-mode .permission-guide li {
  color: #c5cae9;
}

.dark-mode .permission-guide li strong {
  color: #e8eaf6;
}

.dark-mode .permission-guide p {
  color: #9fa8da;
}

.dark-mode .notification-sound-settings {
  background: linear-gradient(135deg, #4a148c 0%, #6a1b9a 100%);
  border: 2px solid #7b1fa2;
  color: #f3e5f5;
}

.dark-mode .notification-sound-settings > label {
  color: #f3e5f5;
}

/* Notification Sound Settings Styles */
.notification-sound-settings {
  border: 2px solid #e8f4fd;
  border-radius: 12px;
  padding: 20px;
  margin: 20px 0;
  background: linear-gradient(135deg, #f8fbff 0%, #e8f4fd 100%);
}

.notification-sound-settings > label {
  font-weight: 600;
  color: var(--primary-color);
  font-size: 16px;
  margin-bottom: 15px;
  display: block;
}

.sound-control-group,
.volume-control-group,
.sound-type-group,
.sound-test-group {
  margin: 15px 0;
  padding: 10px 0;
}

/* Toggle Switch Styles */
.sound-toggle {
  display: flex;
  align-items: center;
  gap: 15px;
}

.switch {
  position: relative;
  display: inline-block;
  width: 60px;
  height: 34px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .4s;
  border-radius: 34px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 26px;
  width: 26px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
}

input:checked + .slider {
  background-color: var(--primary-color);
}

input:checked + .slider:before {
  transform: translateX(26px);
}

.toggle-label {
  font-weight: 500;
  color: var(--text-color);
}

/* Volume Control Styles */
.volume-control-group .volume-label {
  font-weight: 500;
  color: var(--text-color);
  margin-bottom: 10px;
  display: block;
}

.volume-slider-container {
  display: flex;
  align-items: center;
  gap: 15px;
  margin: 10px 0;
}

.volume-slider {
  flex: 1;
  height: 8px;
  border-radius: 5px;
  background: #ddd;
  outline: none;
  opacity: 0.7;
  transition: opacity 0.2s;
  cursor: pointer;
}

.volume-slider:hover {
  opacity: 1;
}

.volume-slider::-webkit-slider-thumb {
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: var(--primary-color);
  cursor: pointer;
  box-shadow: 0 2px 6px rgba(0,0,0,0.2);
  transition: all 0.2s ease;
}

.volume-slider::-webkit-slider-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 3px 8px rgba(0,0,0,0.3);
}

.volume-slider::-moz-range-thumb {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: var(--primary-color);
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 6px rgba(0,0,0,0.2);
}

.volume-display {
  font-weight: 600;
  color: var(--primary-color);
  font-size: 14px;
  min-width: 40px;
  text-align: center;
  font-family: var(--number-font);
}

/* Sound Type Selection */
.sound-type-group label {
  font-weight: 500;
  color: var(--text-color);
  margin-bottom: 8px;
  display: block;
}

.sound-type-group select {
  width: 100%;
  padding: 10px;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  background: white;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.sound-type-group select:focus {
  border-color: var(--primary-color);
  outline: none;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

/* Test Sound Button */
.sound-test-group {
  text-align: center;
  padding-top: 15px;
  border-top: 1px solid #e1e8ed;
}

#testNotificationSound {
  padding: 10px 20px;
  font-size: 14px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

#testNotificationSound:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(23, 162, 184, 0.3);
}

#testNotificationSound:active {
  transform: translateY(0);
}

/* Dark Mode Support for Sound Settings */
[data-theme="dark"] .notification-sound-settings {
  background: linear-gradient(135deg, #2c2c2c 0%, #3a3a3a 100%);
  border-color: #444;
}

[data-theme="dark"] .slider {
  background-color: #555;
}

[data-theme="dark"] .volume-slider {
  background: #555;
}

[data-theme="dark"] .sound-type-group select {
  background: var(--light-color);
  border-color: #555;
  color: var(--text-color);
}

.settings-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.settings-card.danger-card {
  border-color: var(--danger-color);
  background: linear-gradient(
    135deg,
    var(--light-color) 0%,
    rgba(231, 76, 60, 0.05) 100%
  );
}

.settings-header h3 {
  color: var(--text-color);
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.settings-header p {
  color: var(--text-color);
  opacity: 0.8;
  margin-bottom: 20px;
}

.danger-text {
  color: var(--danger-color) !important;
  font-weight: 500;
}

.settings-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  align-items: center;
  margin-top: 10px;
  justify-content: center;
}

/* Demo Data Info Box */
.demo-data-info {
  margin-top: 15px;
  padding: 12px;
  background: rgba(52, 152, 219, 0.1);
  border-radius: 8px;
  font-size: 14px;
  border-left: 4px solid #3498db;
  transition: all 0.3s ease;
}

.demo-data-info:hover {
  background: rgba(52, 152, 219, 0.15);
  transform: translateY(-1px);
}

/* Enhanced Demo Button */
#loadCompleteDemo {
  background: linear-gradient(135deg, #3498db, #2980b9) !important;
  border: none !important;
  color: white !important;
  font-size: 16px !important;
  padding: 15px 30px !important;
  border-radius: 12px !important;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
  position: relative;
  overflow: hidden;
  min-width: 320px;
  text-align: center;
}

#loadCompleteDemo:hover {
  background: linear-gradient(135deg, #2980b9, #1f5f8b) !important;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
}

#loadCompleteDemo:active {
  transform: translateY(0);
  box-shadow: 0 2px 10px rgba(52, 152, 219, 0.3);
}

#loadCompleteDemo::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

#loadCompleteDemo:hover::before {
  left: 100%;
}

#loadCompleteDemo small {
  display: block;
  margin-top: 6px;
  opacity: 0.9;
  font-size: 12px;
  font-weight: normal;
}

.settings-actions .btn {
  min-width: 180px;
  justify-content: center;
  position: relative;
  flex-direction: column;
  gap: 4px;
}

.btn-subtitle {
  font-size: 0.75rem;
  opacity: 0.8;
  font-weight: normal;
  line-height: 1;
}

.settings-info {
  margin-top: 15px;
  padding: 12px;
  background: rgba(52, 152, 219, 0.1);
  border: 1px solid rgba(52, 152, 219, 0.2);
  border-radius: var(--border-radius);
  color: var(--text-color);
}

.settings-info p {
  margin: 0;
  font-size: 0.9rem;
  line-height: 1.4;
}

.settings-info i {
  color: var(--info-color);
  margin-right: 8px;
}

.settings-actions .btn-info {
  background: linear-gradient(135deg, var(--info-color), #0056b3);
  border: none;
  box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
  transition: all 0.3s ease;
}

.settings-actions .btn-info:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
}

/* Settings Path Styles */
.settings-path-container {
  margin-top: 15px;
}

.path-display {
  display: flex;
  align-items: center;
  gap: 10px;
  background: var(--bg-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 5px;
  transition: var(--transition);
}

.path-display:hover {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.1);
}

.path-display input {
  flex: 1;
  border: none;
  background: transparent;
  color: var(--text-color);
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  padding: 8px 12px;
  outline: none;
}

.copy-path-btn {
  min-width: auto !important;
  width: 40px;


  height: 40px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.copy-path-btn:hover {
  background: var(--primary-color);
  color: white;
  transform: scale(1.05);
}

.copy-path-btn.copied {
  background: var(--success-color);
  color: white;
}

.copy-path-btn.copied i:before {
  content: "\f00c"; /* checkmark icon */
}



.file-input-wrapper {
  display: flex;
  align-items: center;
  gap: 15px;
  flex: 1;
}

.file-name {
  color: var(--text-color);
  font-size: 0.9rem;
  opacity: 0.8;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.import-options {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid var(--border-color);
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
  color: var(--text-color);
  font-size: 0.9rem;
}

.checkbox-label input[type="checkbox"] {
  width: 18px;
  height: 18px;
  accent-color: var(--primary-color);
}

/* Enhanced Data Statistics Styles */
.stats-container {
  max-width: 100%;
}

/* Summary Card Styles */
.summary-card {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  color: white;
  border: none;
  box-shadow: 0 10px 30px rgba(52, 152, 219, 0.4);
  position: relative;
  overflow: hidden;
}

.summary-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%, rgba(255, 255, 255, 0.1) 100%);
  pointer-events: none;
}

.summary-card .stats-section-title {
  color: white;
  border-bottom-color: rgba(255, 255, 255, 0.3);
}

.summary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-top: 15px;
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 15px;
  background: rgba(255, 255, 255, 0.1);
  padding: 20px;
  border-radius: var(--border-radius);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: var(--transition);
}

.summary-item:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-2px);
}

.summary-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  flex-shrink: 0;
}

.summary-icon.income {
  background: var(--secondary-color);
  color: white;
}

.summary-icon.expense {
  background: var(--danger-color);
  color: white;
}

.summary-icon.positive {
  background: var(--secondary-color);
  color: white;
}

.summary-icon.negative {
  background: var(--danger-color);
  color: white;
}

.summary-content {
  flex: 1;
}

.summary-content h5 {
  color: white;
  margin-bottom: 5px;
  font-size: 1.2rem;
  opacity: 0.9;
}

.summary-value {
  color: white;
  font-size: 1.2rem;
  font-weight: 700;
  margin-bottom: 3px;
}

.summary-value.positive {
  color: #2ecc71;
}

.summary-value.negative {
  color: #e74c3c;
}

.summary-count {
  color: white;
  font-size: 0.8rem;
  opacity: 0.8;
}

.stats-section {
  background: var(--light-color);
  border-radius: var(--border-radius);
  padding: 25px;
  margin-bottom: 25px;
  border: 2px solid var(--border-color);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: var(--transition);
}

.stats-section:hover {
  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.stats-section-title {
  color: var(--text-color);
  margin-bottom: 20px;
  font-size: 1.2rem;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 10px;
  border-bottom: 3px solid var(--primary-color);
  padding-bottom: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 20px;
  margin-top: 10px;
}

/* Responsive adjustments for stats grid */
@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 15px;
  }

  .stat-item {
    padding: 15px;
  }

  .stat-item .stat-number,
  .stat-item .stat-value {
    font-size: 1.2rem;
  }
}

@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .stats-section {
    padding: 15px;
  }

  .stats-section-title {
    font-size: 1rem;
  }
}

.stat-item {
  background: var(--bg-color);
  padding: 20px;
  border-radius: 12px;
  border: 2px solid var(--border-color);
  text-align: center;
  transition: var(--transition);
  position: relative;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.stat-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-color: var(--primary-color);
}

.stat-item h4, .stat-item h5 {
  color: var(--text-color);
  margin-bottom: 12px;
  font-size: 1.1rem;
  font-weight: 600;
  opacity: 0.9;
  text-transform: capitalize;
}

.stat-item .stat-number, .stat-item .stat-value {
  color: var(--primary-color);
  font-size: 1.4rem;
  font-weight: 700;
  margin-bottom: 8px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.stat-item .stat-detail {
  color: var(--text-color);
  font-size: 0.85rem;
  opacity: 0.8;
  font-weight: 500;
}

/* Color-coded stat items */
.stat-item.highlight {
  border-color: var(--primary-color);
  background: linear-gradient(135deg, var(--bg-color) 0%, rgba(52, 152, 219, 0.1) 100%);
  box-shadow: 0 4px 15px rgba(52, 152, 219, 0.2);
}

.stat-item.positive {
  border-color: var(--secondary-color);
  background: linear-gradient(135deg, var(--bg-color) 0%, rgba(46, 204, 113, 0.1) 100%);
  box-shadow: 0 4px 15px rgba(46, 204, 113, 0.2);
}

.stat-item.positive .stat-value {
  color: var(--secondary-color);
  text-shadow: 0 1px 3px rgba(46, 204, 113, 0.3);
}

.stat-item.negative {
  border-color: var(--danger-color);
  background: linear-gradient(135deg, var(--bg-color) 0%, rgba(231, 76, 60, 0.1) 100%);
  box-shadow: 0 4px 15px rgba(231, 76, 60, 0.2);
}

.stat-item.negative .stat-value {
  color: var(--danger-color);
  text-shadow: 0 1px 3px rgba(231, 76, 60, 0.3);
}

.stat-item.income {
  border-left: 6px solid var(--secondary-color);
  background: linear-gradient(135deg, var(--bg-color) 0%, rgba(46, 204, 113, 0.08) 100%);
}

.stat-item.expense {
  border-left: 6px solid var(--danger-color);
  background: linear-gradient(135deg, var(--bg-color) 0%, rgba(231, 76, 60, 0.08) 100%);
}

.stat-item.loan {
  border-left: 6px solid var(--warning-color);
  background: linear-gradient(135deg, var(--bg-color) 0%, rgba(241, 196, 15, 0.08) 100%);
}

.stat-item.bank {
  border-left: 6px solid var(--primary-color);
  background: linear-gradient(135deg, var(--bg-color) 0%, rgba(52, 152, 219, 0.08) 100%);
}

.stat-item.pending {
  border-left: 6px solid var(--warning-color);
  background: linear-gradient(135deg, var(--bg-color) 0%, rgba(241, 196, 15, 0.08) 100%);
}

.stat-item.paid {
  border-left: 6px solid var(--secondary-color);
  background: linear-gradient(135deg, var(--bg-color) 0%, rgba(46, 204, 113, 0.08) 100%);
}

/* Light Mode Enhancements for Data Statistics */
.stats-section {
  background: #ffffff;
  border: 2px solid #e8f4fd;
  box-shadow: 0 4px 20px rgba(52, 152, 219, 0.1);
}

.stats-section-title {
  color: #2c3e50;
  font-weight: 700;
  border-bottom-color: var(--primary-color);
}

.stat-item {
  background: #ffffff;
  border: 2px solid #e1e8ed;
  color: #2c3e50;
}

.stat-item h4, .stat-item h5 {
  color: #2c3e50;
  font-weight: 600;
}

.stat-item .stat-number, .stat-item .stat-value {
  color: var(--primary-color);
  font-weight: 700;
}

.stat-item .stat-detail {
  color: #5a6c7d;
  font-weight: 500;
}

/* Category Analysis Light Mode */
.category-group {
  background: #ffffff;
  border: 2px solid #e1e8ed;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.category-group h5 {
  color: #2c3e50;
  font-weight: 600;
  border-bottom-color: #e1e8ed;
}

.category-item {
  color: #2c3e50;
}

.category-amount {
  color: var(--primary-color);
  font-weight: 600;
}

/* Dark Mode Support for Data Statistics */
[data-theme="dark"] .stats-section {
  background: var(--light-color);
  border-color: #444;
}

[data-theme="dark"] .stats-section-title {
  color: var(--text-color);
  border-bottom-color: var(--primary-color);
}

[data-theme="dark"] .stat-item {
  background: var(--bg-color);
  border-color: #444;
}

[data-theme="dark"] .stat-item:hover {
  box-shadow: 0 4px 15px rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .stat-item h4,
[data-theme="dark"] .stat-item h5 {
  color: var(--text-color);
}

[data-theme="dark"] .stat-item .stat-number,
[data-theme="dark"] .stat-item .stat-value {
  color: var(--primary-color);
}

[data-theme="dark"] .stat-item .stat-detail {
  color: var(--text-color);
  opacity: 0.7;
}

[data-theme="dark"] .stat-item.highlight {
  border-color: var(--primary-color);
  background: linear-gradient(135deg, var(--bg-color) 0%, rgba(52, 152, 219, 0.1) 100%);
}

[data-theme="dark"] .stat-item.positive {
  border-color: var(--secondary-color);
  background: linear-gradient(135deg, var(--bg-color) 0%, rgba(46, 204, 113, 0.1) 100%);
}

[data-theme="dark"] .stat-item.negative {
  border-color: var(--danger-color);
  background: linear-gradient(135deg, var(--bg-color) 0%, rgba(231, 76, 60, 0.1) 100%);
}

[data-theme="dark"] .category-group {
  background: var(--bg-color);
  border-color: #444;
}

[data-theme="dark"] .category-group h5 {
  color: var(--text-color);
  border-bottom-color: #444;
}

[data-theme="dark"] .category-item {
  color: var(--text-color);
}

[data-theme="dark"] .category-amount {
  color: var(--primary-color);
}

/* Summary Card Dark Mode */
[data-theme="dark"] .summary-card {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .summary-item {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .summary-item:hover {
  background: rgba(255, 255, 255, 0.1);
}

/* Category Management Styles */
.category-type-selector {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.category-type-btn {
  padding: 10px 20px;
  border: 2px solid var(--border-color);
  background: transparent;
  color: var(--text-color);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  gap: 8px;
  font-family: var(--bengali-font);
}

.category-type-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.category-type-btn.active {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.category-type-btn[data-type="income"].active {
  background: var(--secondary-color);
  border-color: var(--secondary-color);
}

.category-type-btn[data-type="expense"].active {
  background: var(--danger-color);
  border-color: var(--danger-color);
}

.add-category-form {
  background: var(--bg-color);
  padding: 20px;
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
  margin-bottom: 25px;
}

.form-row {
  display: flex;
  gap: 15px;
  align-items: end;
  flex-wrap: wrap;
}

.form-row .form-group {
  flex: 1;
  min-width: 200px;
}

.form-row .form-group:last-child {
  flex: 0 0 auto;
}

.form-control {
  width: 100%;
  padding: 12px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  background: var(--light-color);
  color: var(--text-color);
  font-size: 0.9rem;
  font-family: var(--bengali-font);
  transition: var(--transition);
}

.form-control:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.category-lists {
  margin-top: 25px;
}

.category-list h4 {
  color: var(--text-color);
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 2px solid var(--border-color);
  display: flex;
  align-items: center;
  gap: 10px;
  font-family: var(--bengali-font);
}

.category-items {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 15px;
  max-height: 400px;
  overflow-y: auto;
  padding-right: 10px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 15px;
  background: var(--bg-color);
}

/* Custom scrollbar for category items */
.category-items::-webkit-scrollbar {
  width: 8px;
}

.category-items::-webkit-scrollbar-track {
  background: var(--bg-color);
  border-radius: 4px;
}

.category-items::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 4px;
}

.category-items::-webkit-scrollbar-thumb:hover {
  background: var(--primary-color);
}

/* Translation suggestion styles */
.translation-suggestion {
  position: absolute;
  background: #e8f5e8;
  border: 1px solid #4caf50;
  border-radius: 4px;
  padding: 8px;
  font-size: 14px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  z-index: 1000;
  font-family: var(--bengali-font);
}

.suggestion-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.suggestion-text {
  color: #2e7d32;
  font-weight: 500;
  flex: 1;
}

.suggestion-accept,
.suggestion-close {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 3px;
  font-size: 12px;
  transition: all 0.2s ease;
}

.suggestion-accept {
  color: #2e7d32;
  background: rgba(76, 175, 80, 0.1);
}

.suggestion-accept:hover {
  background: rgba(76, 175, 80, 0.2);
  transform: scale(1.05);
}

.suggestion-close {
  color: #666;
  background: rgba(0, 0, 0, 0.05);
}

.suggestion-close:hover {
  background: rgba(0, 0, 0, 0.1);
  transform: scale(1.05);
}

.category-management-item {
  background: var(--light-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 10px;
  transition: var(--transition);
  position: relative;
  min-height: auto;
  height: auto;
}

.category-management-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.category-management-item.default-category {
  border-left: 4px solid var(--info-color);
}

.category-management-item.custom-category {
  border-left: 4px solid var(--warning-color);
}

.category-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.category-item-name {
  font-weight: bold;
  color: var(--text-color);
  font-size: 1rem;
  font-family: var(--bengali-font);
}

.category-item-actions {
  display: flex;
  gap: 5px;
}

.category-action-btn {
  padding: 6px 8px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: var(--transition);
  font-size: 0.8rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.category-edit-btn {
  background: var(--primary-color);
  color: white;
}

.category-delete-btn {
  background: var(--danger-color);
  color: white;
}

.category-action-btn:hover {
  transform: scale(1.1);
}

.category-action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.category-item-value {
  color: var(--text-color);
  opacity: 0.7;
  font-size: 0.85rem;
  font-family: 'Courier New', monospace;
}



/* No categories message */
.no-categories-message {
  text-align: center;
  padding: 30px;
  color: var(--text-color);
  opacity: 0.7;
  font-style: italic;
  background: var(--bg-color);
  border: 2px dashed var(--border-color);
  border-radius: var(--border-radius);
  margin: 20px 0;
}

.no-categories-message p {
  margin: 0;
  font-size: 0.9rem;
  line-height: 1.5;
}

/* Responsive Design for Category Management */
@media (max-width: 768px) {
  .form-row {
    flex-direction: column;
  }

  .form-row .form-group {
    min-width: 100%;
  }

  .category-items {
    grid-template-columns: 1fr;
  }

  .category-type-selector {
    flex-direction: column;
  }

  .category-type-btn {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .category-management-item {
    padding: 12px;
  }

  .category-item-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .category-item-actions {
    align-self: flex-end;
  }

  .add-category-form {
    padding: 15px;
  }

  .form-control {
    padding: 10px;
  }
}

/* Category Analysis Styles */
.category-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.category-group {
  background: var(--bg-color);
  padding: 15px;
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
}

.category-group h5 {
  color: var(--text-color);
  margin-bottom: 12px;
  font-weight: 600;
  text-align: center;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--border-color);
}

.category-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.category-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: var(--light-color);
  border-radius: 6px;
  border: 1px solid var(--border-color);
  transition: var(--transition);
}

.category-item:hover {
  background: var(--primary-color);
  color: white;
}

.category-name {
  font-weight: 500;
  flex: 1;
}

.category-amount {
  font-weight: 600;
  color: var(--primary-color);
  margin-right: 8px;
}

.category-item:hover .category-amount {
  color: white;
}

.category-count {
  font-size: 0.8rem;
  opacity: 0.7;
}

.no-data {
  text-align: center;
  color: var(--text-color);
  opacity: 0.6;
  padding: 20px;
  font-style: italic;
}

/* Insights Section Styles */
.insights-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 15px;
}

.insight-item {
  display: flex;
  align-items: center;
  gap: 15px;
  background: var(--bg-color);
  padding: 20px;
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
  transition: var(--transition);
  position: relative;
  overflow: hidden;
}

.insight-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.insight-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: var(--primary-color);
}

.insight-item.income::before {
  background: var(--secondary-color);
}

.insight-item.expense::before {
  background: var(--danger-color);
}

.insight-item.category::before {
  background: var(--warning-color);
}

.insight-item i {
  font-size: 2rem;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  flex-shrink: 0;
}

.insight-item.income i {
  background: rgba(46, 204, 113, 0.1);
  color: var(--secondary-color);
}

.insight-item.expense i {
  background: rgba(231, 76, 60, 0.1);
  color: var(--danger-color);
}

.insight-item.category i {
  background: rgba(241, 196, 15, 0.1);
  color: var(--warning-color);
}

.insight-content {
  flex: 1;
}

.insight-content h6 {
  color: var(--text-color);
  margin-bottom: 5px;
  font-size: 1.2rem;
  font-weight: 600;
}

.insight-content p {
  color: var(--text-color);
  margin-bottom: 5px;
  font-size: 0.9rem;
  opacity: 0.8;
}

.insight-content span {
  color: var(--primary-color);
  font-weight: 600;
  font-size: 1.5rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-top: 20px;
}

/* Export/Import Animation */
.btn.loading {
  position: relative;
  color: transparent;
}

.btn.loading::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20px;
  height: 20px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  100% {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

/* Report Export Buttons */
.report-export-buttons {
  display: flex;
  gap: 15px;
  margin-top: 15px;
  padding: 15px;
  background: var(--light-color);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
  animation: slideDown 0.3s ease-out;
}

.report-export-buttons .btn {
  flex: 1;
  min-width: 150px;
  font-weight: 500;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design for Settings */
@media (max-width: 768px) {
  .settings-actions {
    flex-direction: column;
    align-items: stretch;
  }

  .file-input-wrapper {
    flex-direction: column;
    align-items: stretch;
  }

  .path-display {
    flex-direction: column;
    gap: 8px;
  }

  .path-display input {
    font-size: 0.8rem;
    text-align: center;
  }

  .copy-path-btn {
    width: 100%;
    height: 45px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .category-stats {
    grid-template-columns: 1fr;
  }

  .stats-section {
    padding: 15px;
    margin-bottom: 15px;
  }

  .stats-section-title {
    font-size: 1rem;
  }

  .stat-item .stat-value {
    font-size: 1.2rem;
  }

  .category-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .category-amount {
    margin-right: 0;
  }

  .summary-grid {
    grid-template-columns: 1fr;
  }

  .summary-item {
    flex-direction: column;
    text-align: center;
    gap: 10px;
  }

  .insights-grid {
    grid-template-columns: 1fr;
  }

  .insight-item {
    flex-direction: column;
    text-align: center;
    gap: 10px;
  }

  .insight-item i {
    margin-bottom: 10px;
  }

  .report-export-buttons {
    flex-direction: column;
  }

  .header-content {
    flex-direction: row;
    align-items: center;
    text-align: center;
  }

  .header-left {
    align-items: center;
  }

  .current-datetime {
    flex-direction: column;
    gap: 4px;
    font-size: 0.65rem;
    margin-top: 3px;
  }

  .datetime-item {
    justify-content: center;
  }

  /* Dashboard Charts Responsive */
  .charts-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .chart-card {
    padding: 20px;
  }

  .chart-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .chart-container {
    height: 250px;
  }
}

/* Medium screens */
@media (min-width: 768px) {
  .charts-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .chart-container {
    height: 300px;
  }
}

/* Large screens */
@media (min-width: 1024px) {
  .charts-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .chart-container {
    height: 320px;
  }
}

@media (min-width: 1200px) {
  .charts-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .chart-container {
    height: 350px;
  }

  .dashboard-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  }
}

@media (min-width: 1400px) {
  .container {
    max-width: 1800px;
    padding: 40px;
  }

  .charts-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
  }

  .chart-container {
    height: 400px;
  }

  .dashboard-grid {
    grid-template-columns: repeat(5, 1fr);
    gap: 25px;
  }

  .stat-card {
    padding: 25px;
    min-height: 100px;
  }

  .stat-icon {
    width: 45px;
    height: 45px;
    font-size: 1.2rem;
  }

  .stat-amount {
    font-size: 1.5rem;
  }

  .stat-info h3 {
    font-size: 1.3rem;
  }
}

/* Scroll Buttons */
.scroll-buttons {
  position: fixed;
  right: 30px;
  bottom: 30px;
  z-index: 1002; /* Higher than floating dashboard toggle */
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.scroll-btn {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  border: none;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--secondary-color)
  );
  color: white;
  font-size: 1.2rem;
  cursor: pointer;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transform: translateX(100px);
}

.scroll-btn.show {
  opacity: 1;
  visibility: visible;
  transform: translateX(0);
}

.scroll-btn:hover {
  transform: translateX(0) scale(1.1);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
  background: linear-gradient(
    135deg,
    var(--secondary-color),
    var(--primary-color)
  );
}

.scroll-btn:active {
  transform: translateX(0) scale(0.95);
}

[data-theme="dark"] .scroll-btn {
  background: linear-gradient(135deg, #34495e, #2c3e50);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.4);
}

[data-theme="dark"] .scroll-btn:hover {
  background: linear-gradient(135deg, #2c3e50, #34495e);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.5);
}

/* Scroll Button Animations */
.scroll-to-top {
  animation-delay: 0.1s;
}

.scroll-to-bottom {
  animation-delay: 0.2s;
}

@keyframes scrollBtnSlideIn {
  from {
    opacity: 0;
    transform: translateX(100px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Developer Credit Section */
.developer-credit {
  margin-top: 40px;
  padding: 0;
  background: transparent;
  position: relative;
  overflow: hidden;
}

.credit-container {
  position: relative;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20px;
  padding: 40px 30px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  animation: creditFadeIn 1s ease-out;
}

[data-theme="dark"] .credit-container {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.credit-content {
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 25px;
  text-align: center;
}

.credit-icon {
  font-size: 3rem;
  color: #ffd700;
  animation: iconPulse 2s ease-in-out infinite;
}

.credit-text {
  flex: 1;
  color: white;
}

.credit-title {
  font-size: 1.2rem;
  font-weight: 400;
  margin-bottom: 8px;
  opacity: 0.9;
  animation: textSlideUp 1s ease-out 0.3s both;
}

.developer-name {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 8px;
  background: linear-gradient(45deg, #ffd700, #ffed4e, #ffd700);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: textSlideUp 1s ease-out 0.5s both,
    gradientShift 3s ease-in-out infinite;
  text-shadow: 0 2px 10px rgba(255, 215, 0, 0.3);
}

.credit-subtitle {
  font-size: 1rem;
  opacity: 0.8;
  font-style: italic;
  animation: textSlideUp 1s ease-out 0.7s both;
}

.credit-decoration {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.floating-icon {
  font-size: 1.5rem;
  color: #ffd700;
  animation: float 3s ease-in-out infinite;
}

.floating-icon:nth-child(1) {
  animation-delay: 0s;
}

.floating-icon:nth-child(2) {
  animation-delay: 1s;
  color: #ff6b6b;
}

.floating-icon:nth-child(3) {
  animation-delay: 2s;
  color: #4ecdc4;
}

.credit-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  pointer-events: none;
}

.bg-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: circleFloat 6s ease-in-out infinite;
}

.circle-1 {
  width: 100px;
  height: 100px;
  top: -50px;
  right: -50px;
  animation-delay: 0s;
}

.circle-2 {
  width: 150px;
  height: 150px;
  bottom: -75px;
  left: -75px;
  animation-delay: 2s;
}

.circle-3 {
  width: 80px;
  height: 80px;
  top: 50%;
  right: 10%;
  animation-delay: 4s;
}

/* Developer Credit Animations */
@keyframes creditFadeIn {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes textSlideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes gradientShift {
  0%,
  100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

@keyframes iconPulse {
  0%,
  100% {
    transform: scale(1);
    filter: brightness(1);
  }
  50% {
    transform: scale(1.1);
    filter: brightness(1.2);
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-10px) rotate(5deg);
  }
  66% {
    transform: translateY(5px) rotate(-5deg);
  }
}

@keyframes circleFloat {
  0%,
  100% {
    transform: translateY(0px) scale(1);
    opacity: 0.1;
  }
  50% {
    transform: translateY(-20px) scale(1.1);
    opacity: 0.2;
  }
}

/* New Features Section Styles */
.new-features-section {
  margin: 30px 0;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
}

.feature-card {
  background: var(--bg-color);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  border: 1px solid var(--border-color);
  overflow: hidden;
  transition: var(--transition);
  position: relative;
}

.feature-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.feature-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: linear-gradient(135deg, var(--primary-color), #2980b9);
  color: white;
  cursor: pointer;
}

.feature-header-controls {
  display: flex;
  align-items: center;
  gap: 15px;
}

.recent-filter-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.print-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  padding: 8px 12px;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 0.9rem;
}

.print-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.print-btn i {
  font-size: 1rem;
}

.feature-header h2 {
  margin: 0;
  font-size: 1.2rem;
  display: flex;
  align-items: center;
  gap: 10px;
}

.feature-toggle {
  background: none;
  border: none;
  color: white;
  font-size: 1.2rem;
  cursor: pointer;
  transition: var(--transition);
  padding: 5px;
  border-radius: 50%;
}

.feature-toggle:hover {
  background: rgba(255, 255, 255, 0.2);
}

.feature-toggle.collapsed {
  transform: rotate(-90deg);
}

.feature-content {
  padding: 20px;
  transition: var(--transition);
  max-height: 770px;
  overflow-y: auto;
  overflow-x: hidden;
}

.feature-content.collapsed {
  display: none;
}

/* Custom Scrollbar for Feature Content */
.feature-content::-webkit-scrollbar {
  width: 8px;
}

.feature-content::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

.feature-content::-webkit-scrollbar-thumb {
  background: var(--primary-color);
  border-radius: 4px;
  transition: background 0.3s ease;
}

.feature-content::-webkit-scrollbar-thumb:hover {
  background: #2980b9;
}

/* Firefox Scrollbar */
.feature-content {
  scrollbar-width: thin;
  scrollbar-color: var(--primary-color) rgba(0, 0, 0, 0.1);
}

/* Specific Heights for Different Feature Contents */
#progressContent {
  max-height: 350px;
}

#categoryContent {
  max-height: 450px;
}

#reminderContent {
  max-height: 740px;
}

/* Scroll Indicator for Feature Content */
.feature-content::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 30px;
  height: 3px;
  background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.feature-content.has-scroll::after {
  opacity: 0.7;
}

.feature-content.scrolled-to-bottom::after {
  opacity: 0;
}

/* Recent Transactions Section - Full Width */
.recent-transactions-section {
  width: 100%;
  margin-bottom: 30px;
}

.recent-transactions-section.full-width {
  grid-column: 1 / -1;
}

/* Recent Transactions Specific Styles */
.recent-transactions-card .feature-header {
  background: linear-gradient(135deg, #9b59b6, #8e44ad);
}

.recent-transactions-card .feature-content {
  padding: 0;
}

.recent-transactions-card .transaction-list {
  max-height: 740px;
  overflow-y: auto;
  padding: 20px;
}

/* Custom Scrollbar for Recent Transactions */
.recent-transactions-card .transaction-list::-webkit-scrollbar {
  width: 8px;
}

.recent-transactions-card .transaction-list::-webkit-scrollbar-track {
  background: var(--border-color);
  border-radius: 4px;
}

.recent-transactions-card .transaction-list::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #9b59b6, #8e44ad);
  border-radius: 4px;
}

.recent-transactions-card .transaction-list::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #8e44ad, #7d3c98);
}

/* Firefox scrollbar */
.recent-transactions-card .transaction-list {
  scrollbar-width: thin;
  scrollbar-color: #9b59b6 var(--border-color);
}

/* Recent transactions item styling */
.recent-transactions-card .transaction-item {
  margin-bottom: 8px;
  border-left: 4px solid transparent;
  transition: all 0.3s ease;
  position: relative;
  cursor: pointer;
  padding-top: 12px; /* Reduced space for category badge */
}

.recent-transactions-card .transaction-item:hover {
  transform: translateX(5px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  background: rgba(var(--primary-color-rgb), 0.05);
  border-color: rgba(var(--primary-color-rgb), 0.2);
}

.recent-transactions-card .transaction-item:active {
  transform: translateX(3px) scale(0.98);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* Transaction edit hint */
.transaction-edit-hint {
  position: absolute;
  bottom: 8px;
  right: 15px;
  display: flex;
  align-items: center;
  gap: 8px;
  opacity: 0;
  transition: all 0.3s ease;
  background: linear-gradient(135deg, var(--primary-color), #2980b9);
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  pointer-events: none;
  z-index: 10;
  box-shadow: 0 4px 12px rgba(var(--primary-color-rgb), 0.3);
}

.transaction-item:hover .transaction-edit-hint {
  opacity: 1;
  transform: translateX(-10px);
}

.transaction-edit-hint i {
  font-size: 0.9rem;
  animation: pulse 2s infinite;
}

.transaction-edit-hint span {
  font-family: var(--bengali-font);
  white-space: nowrap;
  font-weight: 500;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

/* Add a subtle edit icon indicator */
.recent-transactions-card .transaction-item::after {
  content: '\f044';
  font-family: 'Font Awesome 6 Free';
  font-weight: 900;
  position: absolute;
  bottom: 8px;
  left: 15px;
  font-size: 0.8rem;
  color: var(--primary-color);
  opacity: 0;
  transition: all 0.3s ease;
  z-index: 6;
}

.recent-transactions-card .transaction-item:hover::after {
  opacity: 0.6;
}

.recent-transactions-card .transaction-item.income {
  border-left-color: var(--secondary-color);
}

.recent-transactions-card .transaction-item.expense {
  border-left-color: var(--danger-color);
}

.recent-transactions-card .transaction-item.loan-given {
  border-left-color: var(--warning-color);
}

.recent-transactions-card .transaction-item.loan-taken {
  border-left-color: var(--info-color);
}

.recent-transactions-card .transaction-item.bank-deposit {
  border-left-color: var(--secondary-color);
}

.recent-transactions-card .transaction-item.bank-withdraw {
  border-left-color: var(--danger-color);
}

/* Transaction Category Badge */
.transaction-category-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 5px 10px;
  border-radius: 15px;
  font-size: 0.75rem;
  font-weight: 600;
  font-family: var(--bengali-font);
  z-index: 5;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  transform: scale(0.95);
}

.transaction-item:hover .transaction-category-badge {
  transform: scale(1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.transaction-category-badge i {
  font-size: 0.7rem;
}

.transaction-category-badge span {
  white-space: nowrap;
}

/* Category Badge Colors */
.transaction-category-badge.income {
  background: linear-gradient(135deg, rgba(46, 204, 113, 0.9), rgba(39, 174, 96, 0.9));
  color: white;
}

.transaction-category-badge.expense {
  background: linear-gradient(135deg, rgba(231, 76, 60, 0.9), rgba(192, 57, 43, 0.9));
  color: white;
}

.transaction-category-badge.loan-given {
  background: linear-gradient(135deg, rgba(243, 156, 18, 0.9), rgba(211, 84, 0, 0.9));
  color: white;
}

.transaction-category-badge.loan-taken {
  background: linear-gradient(135deg, rgba(23, 162, 184, 0.9), rgba(20, 143, 119, 0.9));
  color: white;
}

.transaction-category-badge.bank-deposit {
  background: linear-gradient(135deg, rgba(52, 152, 219, 0.9), rgba(41, 128, 185, 0.9));
  color: white;
}

.transaction-category-badge.bank-withdraw {
  background: linear-gradient(135deg, rgba(155, 89, 182, 0.9), rgba(142, 68, 173, 0.9));
  color: white;
}

/* Mobile responsive for category badge */
@media (max-width: 768px) {
  .transaction-category-badge {
    font-size: 0.7rem;
    padding: 3px 6px;
    gap: 3px;
    top: 6px;
    right: 6px;
  }

  .transaction-category-badge i {
    font-size: 0.65rem;
  }

  .transaction-amount {
    margin-top: 20px;
    font-size: 1.8rem;
  }
}

@media (max-width: 480px) {
  .transaction-category-badge span {
    display: none; /* Hide text on very small screens, show only icon */
  }

  .transaction-category-badge {
    padding: 4px;
    border-radius: 50%;
    width: 22px;
    height: 22px;
    justify-content: center;
    top: 4px;
    right: 4px;
  }

  .transaction-amount {
    margin-top: 18px;
    font-size: 1.6rem;
  }
}

/* Transaction count badge */
.transaction-count {
  background: rgba(255, 255, 255, 0.2);
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.85rem;
  font-weight: 600;
  margin-left: 8px;
}

/* Scroll indicator */
.dashboard-scroll-indicator {
  text-align: center;
  padding: 15px;
  color: var(--text-color);
  opacity: 0.5;
  font-size: 0.9rem;
  border-top: 1px solid var(--border-color);
  margin-top: 10px;
  background: linear-gradient(to bottom, transparent, rgba(155, 89, 182, 0.03));
  transition: opacity 0.3s ease;
  display: none; /* Hide by default to reduce distraction */
}

.dashboard-scroll-indicator:hover {
  opacity: 0.8;
}

.dashboard-scroll-indicator i {
  margin-right: 8px;
  /* Removed bounce animation */
}

/* Removed distracting animations for scroll indicator */

/* View all transactions button */
.view-all-transactions {
  text-align: center;
  padding: 15px 20px;
  border-top: 1px solid var(--border-color);
  background: rgba(155, 89, 182, 0.02);
}

.view-all-transactions .btn {
  min-width: 200px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.view-all-transactions .btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(155, 89, 182, 0.3);
}

/* Progress Tracking Styles */
.progress-tracking-card .feature-header {
  background: linear-gradient(135deg, #2ecc71, #27ae60);
}

.progress-stats {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.progress-item {
  background: var(--light-color);
  padding: 20px;
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.progress-info h4 {
  margin: 0;
  color: var(--text-color);
  font-size: 1rem;
}

.progress-amounts {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 0.9rem;
}

.current-amount {
  font-weight: bold;
  color: var(--primary-color);
}

.vs {
  color: var(--text-color);
  opacity: 0.7;
}

.previous-amount {
  color: var(--text-color);
  opacity: 0.8;
}

.progress-bar {
  height: 8px;
  background: #e0e0e0;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 10px;
}

.progress-fill {
  height: 100%;
  border-radius: 4px;
  transition: width 0.5s ease;
}

.income-progress {
  background: linear-gradient(90deg, #2ecc71, #27ae60);
}

.expense-progress {
  background: linear-gradient(90deg, #e74c3c, #c0392b);
}

.savings-progress {
  background: linear-gradient(90deg, #f39c12, #e67e22);
}

.progress-percentage {
  text-align: right;
  font-size: 0.9rem;
  font-weight: bold;
}

.progress-percentage.positive {
  color: #2ecc71;
}

.progress-percentage.negative {
  color: #e74c3c;
}

/* Category Analysis Styles */
.category-analysis-card .feature-header {
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 12px 12px 0 0;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.category-period-selector {
  margin-bottom: 25px;
  text-align: center;
}

.category-period-selector select {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border: 2px solid #dee2e6;
  border-radius: 25px;
  padding: 12px 20px;
  font-size: 14px;
  font-weight: 500;
  color: #495057;
  transition: all 0.3s ease;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.category-period-selector select:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
  outline: none;
}

.category-analysis-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.category-item {
  background: linear-gradient(135deg, #ffffff, #f8f9fa);
  padding: 20px;
  border-radius: 15px;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
}

.category-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(135deg, #667eea, #764ba2);
  transition: width 0.3s ease;
}

.category-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-color: #667eea;
}

.category-item:hover::before {
  width: 8px;
}

.category-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.category-name {
  font-weight: 600;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 1rem;
}

.category-name::before {
  content: '📊';
  font-size: 1.2rem;
}

.category-amount {
  font-weight: 700;
  color: #667eea;
  font-size: 1.3rem;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.category-bar {
  height: 8px;
  background: linear-gradient(135deg, #e9ecef, #f8f9fa);
  border-radius: 10px;
  overflow: hidden;
  margin-bottom: 12px;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.category-fill {
  height: 100%;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 10px;
  transition: width 0.8s ease;
  position: relative;
}

.category-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.3), transparent);
  border-radius: 10px;
}

.category-percentage {
  font-size: 0.9rem;
  color: #6c757d;
  background: #f8f9fa;
  padding: 4px 12px;
  border-radius: 20px;
  display: inline-block;
  font-weight: 500;
}

.top-categories {
  background: linear-gradient(135deg, #ffffff, #f8f9fa);
  padding: 25px;
  border-radius: 15px;
  border: 1px solid #e9ecef;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
}

.top-categories h4 {
  color: #2c3e50;
  margin-bottom: 20px;
  font-weight: 600;
  text-align: center;
  padding-bottom: 15px;
  border-bottom: 3px solid #667eea;
  position: relative;
  font-size: 1.1rem;
}

.top-categories h4::after {
  content: '';
  position: absolute;
  bottom: -3px;
  left: 50%;
  transform: translateX(-50%);
  width: 50px;
  height: 3px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 2px;
}

.top-category-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.top-category-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background: linear-gradient(135deg, #f8f9fa, #ffffff);
  border-radius: 12px;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.top-category-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 0;
  height: 100%;
  background: linear-gradient(135deg, #667eea, #764ba2);
  transition: width 0.3s ease;
  z-index: 1;
}

.top-category-item:hover {
  color: white;
  transform: translateX(8px);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.top-category-item:hover::before {
  width: 100%;
}

.top-category-rank {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.9rem;
  font-weight: bold;
  position: relative;
  z-index: 2;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.top-category-info {
  flex: 1;
  margin-left: 15px;
  position: relative;
  z-index: 2;
}

.top-category-name {
  font-weight: 500;
  color: #2c3e50;
  font-size: 0.95rem;
  margin-bottom: 2px;
}

.top-category-amount {
  font-weight: 700;
  color: #667eea;
  font-size: 1rem;
}

/* Bottom Grid Layout */
.bottom-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-top: 20px;
}

/* Loan Reminders Styles */
.loan-reminders-card .feature-header {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
}

.reminder-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-bottom: 25px;
}

.reminder-stat {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  background: var(--light-color);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
}

.reminder-stat .stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  color: white;
}

.reminder-stat .stat-icon.overdue {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
}

.reminder-stat .stat-icon.due-soon {
  background: linear-gradient(135deg, #f39c12, #e67e22);
}

.reminder-stat .stat-icon.pending {
  background: linear-gradient(135deg, #3498db, #2980b9);
}

.reminder-stat .stat-info h4 {
  margin: 0 0 5px 0;
  color: var(--text-color);
  font-size: 0.9rem;
}

.reminder-stat .stat-count {
  font-size: 1.5rem;
  font-weight: bold;
  color: var(--primary-color);
}

.reminder-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.reminder-item {
  padding: 15px;
  background: var(--light-color);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
  border-left: 4px solid var(--primary-color);
  transition: var(--transition);
}

.reminder-item:hover {
  transform: translateX(5px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.reminder-item.overdue {
  border-left-color: #e74c3c;
  background: rgba(231, 76, 60, 0.05);
}

.reminder-item.due-soon {
  border-left-color: #f39c12;
  background: rgba(243, 156, 18, 0.05);
}

.reminder-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.reminder-person {
  font-weight: bold;
  color: var(--text-color);
}

.reminder-amount {
  font-weight: bold;
  color: var(--primary-color);
}

.reminder-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.9rem;
  color: var(--text-color);
  opacity: 0.8;
}

.reminder-due-date {
  display: flex;
  align-items: center;
  gap: 5px;
}

.reminder-status {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: bold;
}

.reminder-status.overdue {
  background: #e74c3c;
  color: white;
}

.reminder-status.due-soon {
  background: #f39c12;
  color: white;
}

.reminder-status.pending {
  background: #3498db;
  color: white;
}

/* Responsive Design for New Features */
@media (max-width: 768px) {
  .new-features-section {
    margin: 20px 0;
    gap: 15px;
    grid-template-columns: 1fr;
  }

  .feature-header {
    padding: 15px;
  }

  .feature-header h2 {
    font-size: 1.1rem;
  }

  .feature-content {
    padding: 15px;
  }

  .progress-stats {
    gap: 15px;
  }

  .progress-item {
    padding: 15px;
  }

  .progress-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .category-analysis-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .reminder-stats {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .reminder-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }

  .reminder-details {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}

@media (max-width: 480px) {
  .feature-header {
    padding: 12px;
  }

  .feature-content {
    padding: 12px;
  }

  .progress-amounts {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }

  .top-category-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .top-category-rank {
    align-self: flex-start;
  }
}

/* Developer Credit Responsive Design */
@media (max-width: 768px) {
  .credit-container {
    padding: 30px 20px;
  }

  .credit-content {
    flex-direction: column;
    gap: 20px;
  }

  .developer-name {
    font-size: 2rem;
  }

  .credit-title {
    font-size: 1.1rem;
  }

  .credit-decoration {
    flex-direction: row;
    justify-content: center;
  }

  .credit-icon {
    font-size: 2.5rem;
  }
}

@media (max-width: 480px) {
  .developer-name {
    font-size: 1.8rem;
  }

  .credit-title {
    font-size: 1rem;
  }

  .credit-subtitle {
    font-size: 0.9rem;
  }

  .credit-container {
    padding: 25px 15px;
  }

  /* Scroll Buttons Mobile */
  .scroll-buttons {
    right: 20px;
    bottom: 20px;
    gap: 8px;
  }

  .scroll-btn {
    width: 45px;
    height: 45px;
    font-size: 1.1rem;
  }
}

/* Modern Notes System Styles */
.modern-notes-container {
  height: calc(100vh - 120px);
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  animation: slideInUp 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Notes Header */
.notes-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 30px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-bottom: 3px solid rgba(255, 255, 255, 0.2);
}

.header-left h2 {
  margin: 0;
  font-size: 1.8rem;
  font-weight: 600;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.notes-subtitle {
  font-size: 0.9rem;
  opacity: 0.9;
  margin-top: 5px;
  display: block;
}

.quick-actions {
  display: flex;
  gap: 10px;
}

.quick-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  padding: 10px 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.quick-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* Main Content Area */
.notes-main-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.notes-tabs-wrapper {
  display: flex;
  align-items: center;
  overflow-x: auto;
  scrollbar-width: thin;
  scrollbar-color: #ccc transparent;
}

.notes-tabs-wrapper::-webkit-scrollbar {
  height: 4px;
}

.notes-tabs-wrapper::-webkit-scrollbar-track {
  background: transparent;
}

.notes-tabs-wrapper::-webkit-scrollbar-thumb {
  background: #ccc;
  border-radius: 2px;
}

.notes-tabs-wrapper::-webkit-scrollbar-thumb:hover {
  background: #999;
}

.notes-tabs {
  display: flex;
  flex: 1;
  min-width: 0;
}

.notes-tab {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background: #e9ecef;
  border: none;
  border-right: 1px solid #dee2e6;
  cursor: pointer;
  font-family: "Noto Sans Bengali", sans-serif;
  font-size: 14px;
  font-weight: 500;
  color: #495057;
  transition: all 0.3s ease;
  min-width: 120px;
  max-width: 200px;
  position: relative;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.notes-tab:hover {
  background: #dee2e6;
  color: #212529;
}

.notes-tab.active {
  background: white;
  color: #8e44ad;
  border-bottom: 3px solid #8e44ad;
  font-weight: 600;
}

.notes-tab-title {
  flex: 1;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  border: none;
  background: transparent;
  font-family: inherit;
  font-size: inherit;
  color: inherit;
  padding: 0;
  margin: 0;
}

.notes-tab-title:focus {
  outline: none;
  background: rgba(142, 68, 173, 0.1);
  border-radius: 3px;
  padding: 2px 4px;
}

.notes-tab-title:hover {
  background: rgba(142, 68, 173, 0.05);
  border-radius: 3px;
}

.notes-tab-close {
  margin-left: 8px;
  padding: 2px 4px;
  background: none;
  border: none;
  color: #6c757d;
  cursor: pointer;
  border-radius: 3px;
  font-size: 12px;
  opacity: 0;
  transition: all 0.2s ease;
  position: relative;
}

.notes-tab-close::after {
  content: 'বন্ধ করুন (Ctrl+Shift+W)';
  position: absolute;
  bottom: -30px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 6px;
  border-radius: 3px;
  font-size: 11px;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease;
  z-index: 1000;
}

.notes-tab-close:hover::after {
  opacity: 1;
}

.notes-tab:hover .notes-tab-close {
  opacity: 1;
}

.notes-tab-close:hover {
  background: #dc3545;
  color: white;
  transform: scale(1.1);
}

.add-tab-btn {
  padding: 12px 16px;
  background: #8e44ad;
  border: none;
  color: white;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
  min-width: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.add-tab-btn::after {
  content: 'নতুন ট্যাব (Ctrl+Shift+T)';
  position: absolute;
  bottom: -35px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 5px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease;
  z-index: 1000;
}

.add-tab-btn:hover::after {
  opacity: 1;
}

.tab-counter {
  padding: 12px 16px;
  background: #f8f9fa;
  border-left: 1px solid #dee2e6;
  color: #6c757d;
  font-size: 12px;
  font-weight: 500;
  display: flex;
  align-items: center;
  min-width: 50px;
  justify-content: center;
}

#tabCount {
  color: #8e44ad;
  font-weight: 600;
}

.tab-help-btn {
  padding: 12px 16px;
  background: #17a2b8;
  border: none;
  color: white;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
  min-width: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-left: 1px solid #dee2e6;
}

.tab-help-btn:hover {
  background: #138496;
  transform: scale(1.05);
}

.tab-menu-btn {
  padding: 12px 16px;
  background: #6c757d;
  border: none;
  color: white;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
  min-width: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-left: 1px solid #dee2e6;
}

.tab-menu-btn:hover {
  background: #5a6268;
  transform: scale(1.05);
}

.add-tab-btn:hover {
  background: #7d3c98;
  transform: scale(1.05);
}

/* Tab system animations */
.notes-tab {
  transition: all 0.3s ease;
}

.notes-tab:not(.active):hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.notes-tabs-container {
  position: relative;
  z-index: 1;
}

/* Ensure tab content is properly styled */
.notes-editor-container {
  position: relative;
  z-index: 0;
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInTab {
  from {
    opacity: 0;
    transform: translateX(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

.notes-tab {
  animation: slideInTab 0.3s ease-out;
}

/* Tab indicator for active tab */
.notes-tab.active::before {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #8e44ad, #9b59b6);
  border-radius: 3px 3px 0 0;
}

/* Drag and drop styles for tabs */
.notes-tab.dragging {
  opacity: 0.5;
  transform: rotate(5deg);
  z-index: 1000;
}

.notes-tab[draggable="true"] {
  cursor: move;
}

.notes-tab[draggable="true"]:hover {
  cursor: grab;
}

.notes-tab[draggable="true"]:active {
  cursor: grabbing;
}

/* Tab context menu */
.tab-context-menu {
  position: absolute;
  background: white;
  border: 1px solid #ddd;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 10000;
  min-width: 200px;
  padding: 8px 0;
  font-family: "Noto Sans Bengali", sans-serif;
}

.context-menu-item {
  padding: 10px 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 14px;
  color: #333;
  transition: background 0.2s ease;
}

.context-menu-item:hover {
  background: #f8f9fa;
}

.context-menu-item.danger {
  color: #dc3545;
}

.context-menu-item.danger:hover {
  background: #f8d7da;
}

.context-menu-separator {
  height: 1px;
  background: #eee;
  margin: 4px 0;
}

.context-menu-item i {
  width: 16px;
  text-align: center;
}

/* Tab help modal */
.tab-help-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  animation: fadeIn 0.3s ease;
}

.tab-help-content {
  background: white;
  border-radius: 10px;
  max-width: 600px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  font-family: "Noto Sans Bengali", sans-serif;
}

.tab-help-header {
  background: linear-gradient(135deg, #17a2b8, #20c997);
  color: white;
  padding: 20px;
  border-radius: 10px 10px 0 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.tab-help-header h3 {
  margin: 0;
  font-size: 1.3rem;
  display: flex;
  align-items: center;
  gap: 10px;
}

.tab-help-close {
  background: none;
  border: none;
  color: white;
  font-size: 18px;
  cursor: pointer;
  padding: 5px;
  border-radius: 50%;
  transition: background 0.3s ease;
}

.tab-help-close:hover {
  background: rgba(255, 255, 255, 0.2);
}

.tab-help-body {
  padding: 25px;
}

.tab-help-body h4 {
  color: #333;
  margin: 20px 0 10px 0;
  font-size: 1.1rem;
  display: flex;
  align-items: center;
  gap: 8px;
}

.tab-help-body h4:first-child {
  margin-top: 0;
}

.tab-help-body ul {
  margin: 10px 0 20px 0;
  padding-left: 20px;
}

.tab-help-body li {
  margin: 8px 0;
  line-height: 1.5;
}

.tab-help-body strong {
  color: #8e44ad;
  background: #f8f9fa;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: monospace;
}

.tab-help-footer {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.tab-help-footer .btn {
  padding: 10px 20px;
  font-size: 1rem;
}

/* Tab quick menu */
.tab-quick-menu {
  position: absolute;
  background: white;
  border: 1px solid #ddd;
  border-radius: 8px;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
  z-index: 10000;
  min-width: 250px;
  padding: 0;
  font-family: "Noto Sans Bengali", sans-serif;
  overflow: hidden;
}

.quick-menu-header {
  background: linear-gradient(135deg, #6c757d, #5a6268);
  color: white;
  padding: 12px 16px;
  font-weight: 600;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.quick-menu-item {
  padding: 12px 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  color: #333;
  transition: background 0.2s ease;
  border-bottom: 1px solid #f8f9fa;
}

.quick-menu-item:hover {
  background: #f8f9fa;
}

.quick-menu-item:last-child {
  border-bottom: none;
}

.quick-menu-item i {
  margin-right: 10px;
  width: 16px;
  text-align: center;
  color: #6c757d;
}

.shortcut {
  font-size: 11px;
  color: #6c757d;
  background: #f8f9fa;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: monospace;
}

.quick-menu-separator {
  height: 1px;
  background: #eee;
  margin: 4px 0;
}

.notes-header {
  background: linear-gradient(135deg, #8e44ad, #9b59b6);
  color: white;
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 15px;
}

.notes-header h2 {
  margin: 0;
  font-size: 1.5rem;
  display: flex;
  align-items: center;
  gap: 10px;
}

.notes-toolbar {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.notes-toolbar .btn {
  padding: 8px 16px;
  font-size: 0.9rem;
  border-radius: 5px;
  border: none;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  gap: 5px;
}

.notes-toolbar .btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.notes-editor-container {
  background: var(--bg-color);
  min-height: 600px;
  display: flex;
  flex-direction: column;
}

/* Rich Text Editor Toolbar */
.editor-toolbar {
  background: var(--light-color);
  border-bottom: 1px solid var(--border-color);
  padding: 10px 15px;
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  align-items: center;
  position: sticky;
  top: 0;
  z-index: 10;
}

.toolbar-group {
  display: flex;
  gap: 2px;
  align-items: center;
}

.toolbar-btn {
  background: transparent;
  border: 1px solid transparent;
  padding: 8px 10px;
  border-radius: 4px;
  cursor: pointer;
  color: var(--text-color);
  transition: var(--transition);
  font-size: 0.9rem;
  min-width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.toolbar-btn:hover {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.toolbar-btn.active {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

/* Delete all images button */
.toolbar-btn#deleteAllImages {
  color: #e67e22;
  border-color: transparent;
  position: relative;
}

.toolbar-btn#deleteAllImages:hover {
  background: #e67e22;
  color: white;
  border-color: #e67e22;
  transform: scale(1.05);
}

.toolbar-btn#deleteAllImages:active {
  background: #d35400;
  border-color: #d35400;
}

/* Warning tooltip for delete images button */
.toolbar-btn#deleteAllImages::after {
  content: "সব ছবি মুছে ফেলবে!";
  position: absolute;
  bottom: -35px;
  left: 50%;
  transform: translateX(-50%);
  background: #e67e22;
  color: white;
  padding: 6px 10px;
  border-radius: 4px;
  font-size: 0.75rem;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease;
  z-index: 1000;
}

.toolbar-btn#deleteAllImages::before {
  content: "";
  position: absolute;
  bottom: -30px;
  left: 50%;
  transform: translateX(-50%);
  border: 5px solid transparent;
  border-bottom-color: #e67e22;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease;
  z-index: 1001;
}

.toolbar-btn#deleteAllImages:hover::after,
.toolbar-btn#deleteAllImages:hover::before {
  opacity: 1;
}

.toolbar-separator {
  width: 1px;
  height: 24px;
  background: var(--border-color);
  margin: 0 8px;
}

.toolbar-select {
  padding: 6px 8px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background: var(--bg-color);
  color: var(--text-color);
  font-size: 0.85rem;
  cursor: pointer;
  min-width: 80px;
}

.toolbar-color {
  width: 36px;
  height: 36px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  cursor: pointer;
  background: none;
  padding: 2px;
}

.toolbar-search {
  padding: 6px 8px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background: var(--bg-color);
  color: var(--text-color);
  font-size: 0.85rem;
  min-width: 120px;
  outline: none;
}

.toolbar-search:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

/* Rich Text Editor */
.notes-editor {
  flex: 1;
  padding: 20px;
  min-height: 500px;
  outline: none;
  font-family: var(--bengali-font);
  font-size: 20px; /* Increased from 16px */
  line-height: 1.6;
  color: var(--text-color);
  background: var(--bg-color);
  border: none;
  resize: none;
  overflow-y: auto;
}

.notes-editor:empty:before {
  content: attr(placeholder);
  color: #999;
  font-style: italic;
  pointer-events: none;
}

.notes-editor:focus {
  outline: none;
}

/* Editor Content Styles */
.notes-editor h1,
.notes-editor h2,
.notes-editor h3,
.notes-editor h4,
.notes-editor h5,
.notes-editor h6 {
  margin: 20px 0 10px 0;
  color: var(--text-color);
}

.notes-editor p {
  margin: 30px 0;
}

.notes-editor ul,
.notes-editor ol {
  margin: 10px 0;
  padding-left: 30px;
}

.notes-editor li {
  margin: 5px 0;
}

.notes-editor a {
  color: var(--primary-color);
  text-decoration: underline;
}

.notes-editor a:hover {
  color: var(--secondary-color);
}

.notes-editor blockquote {
  border-left: 4px solid var(--primary-color);
  margin: 15px 0;
  padding: 10px 15px;
  background: var(--light-color);
  font-style: italic;
}

.notes-editor hr {
  border: none;
  height: 2px;
  background: var(--border-color);
  margin: 20px 0;
}

.notes-editor table {
  border-collapse: collapse;
  width: 100%;
  margin: 15px 0;
}

.notes-editor table th,
.notes-editor table td {
  border: 1px solid var(--border-color);
  padding: 8px 12px;
  text-align: left;
}

.notes-editor table th {
  background: var(--light-color);
  font-weight: 600;
}

/* Search Highlights in Notes */
.search-highlight {
  background: #ffeb3b;
  color: #333;
  padding: 2px 4px;
  border-radius: 2px;
  font-weight: 500;
}

.search-highlight.current-highlight {
  background: #ff9800;
  color: white;
  animation: pulse 1s ease-in-out infinite alternate;
}

@keyframes pulse {
  0% { opacity: 0.7; }
  100% { opacity: 1; }
}

/* Images in Notes Editor */
.notes-editor img {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  margin: 10px 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: var(--transition);
  position: relative;
  resize: both;
  overflow: hidden;
}

.notes-editor img:hover {
  transform: scale(1.02);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

/* Image wrapper for resize functionality */
.image-wrapper {
  display: inline-block;
  position: relative;
  margin: 10px 0;
  border: 2px solid transparent;
  border-radius: 8px;
  transition: var(--transition);
}

.image-wrapper:hover {
  border-color: var(--primary-color);
}

.image-wrapper.selected {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.3);
}

.image-wrapper img {
  display: block;
  margin: 0;
  border-radius: 6px;
}

/* Resize handles */
.resize-handle {
  position: absolute;
  background: var(--primary-color);
  border: 2px solid white;
  border-radius: 50%;
  width: 12px;
  height: 12px;
  opacity: 0;
  transition: all 0.2s ease;
  z-index: 10;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.resize-handle:hover {
  background: var(--secondary-color);
  transform: scale(1.2);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.3);
}

.image-wrapper:hover .resize-handle,
.image-wrapper.selected .resize-handle {
  opacity: 1;
}

.image-wrapper.resizing .resize-handle {
  opacity: 1;
  background: var(--secondary-color);
}

.resize-handle.nw {
  top: -6px;
  left: -6px;
  cursor: nw-resize;
}

.resize-handle.ne {
  top: -6px;
  right: -6px;
  cursor: ne-resize;
}

.resize-handle.sw {
  bottom: -6px;
  left: -6px;
  cursor: sw-resize;
}

.resize-handle.se {
  bottom: -6px;
  right: -6px;
  cursor: se-resize;
}

.resize-handle.n {
  top: -6px;
  left: 50%;
  transform: translateX(-50%);
  cursor: n-resize;
}

.resize-handle.s {
  bottom: -6px;
  left: 50%;
  transform: translateX(-50%);
  cursor: s-resize;
}

.resize-handle.w {
  left: -6px;
  top: 50%;
  transform: translateY(-50%);
  cursor: w-resize;
}

.resize-handle.e {
  right: -6px;
  top: 50%;
  transform: translateY(-50%);
  cursor: e-resize;
}

/* Image size indicator */
.image-size-indicator {
  position: absolute;
  top: -30px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  opacity: 0;
  transition: opacity 0.2s ease;
  pointer-events: none;
  z-index: 11;
}

.image-wrapper.resizing .image-size-indicator {
  opacity: 1;
}

.notes-editor img.small {
  max-width: 200px;
}

.notes-editor img.medium {
  max-width: 400px;
}

.notes-editor img.large {
  max-width: 600px;
}

.notes-editor img.full {
  max-width: 100%;
}

/* Image context menu */
.image-context-menu {
  position: absolute;
  background: var(--bg-color);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 8px 0;
  z-index: 1000;
  min-width: 150px;
  display: none;
}

.image-context-menu-item {
  padding: 8px 16px;
  cursor: pointer;
  font-size: 0.9rem;
  color: var(--text-color);
  transition: var(--transition);
}

.image-context-menu-item:hover {
  background: var(--light-color);
}

.image-context-menu-item i {
  margin-right: 8px;
  width: 16px;
}

/* Date/Time stamps in notes */
.notes-editor .datetime-stamp {
  background: var(--light-color);
  color: var(--primary-color);
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.9em;
  font-weight: 500;
  display: inline-block;
  margin: 2px;
  border: 1px solid var(--border-color);
}

[data-theme="dark"] .notes-editor .datetime-stamp {
  background: #2c2c2c;
  color: var(--primary-color);
  border-color: #444;
}

/* Image Modal for full view */
.image-modal {
  display: none;
  position: fixed;
  z-index: 10000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.9);
  animation: fadeIn 0.3s ease;
}

.image-modal-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  max-width: 90%;
  max-height: 90%;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
}

.image-modal-controls {
  position: absolute;
  top: 20px;
  right: 30px;
  display: flex;
  align-items: center;
  gap: 15px;
  z-index: 10001;
}

.image-modal-close {
  color: white;
  font-size: 40px;
  font-weight: bold;
  cursor: pointer;
  transition: var(--transition);
}

.image-modal-close:hover {
  color: #ff6b6b;
  transform: scale(1.1);
}

.image-modal-fullscreen {
  color: white !important;
  background: rgba(255, 255, 255, 0.2) !important;
  border: 2px solid rgba(255, 255, 255, 0.3) !important;
  width: 40px !important;
  height: 40px !important;
  font-size: 16px !important;
}

.image-modal-fullscreen:hover {
  background: rgba(255, 255, 255, 0.3) !important;
  border-color: rgba(255, 255, 255, 0.5) !important;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* Drag and Drop Styles */
.notes-editor.drag-over {
  background: rgba(52, 152, 219, 0.1) !important;
  border: 2px dashed var(--primary-color) !important;
  transition: all 0.3s ease;
}

.notes-editor::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.notes-editor.drag-over::after {
  content: 'ছবি এখানে ড্রপ করুন';
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(52, 152, 219, 0.9);
  color: white;
  font-size: 1.2rem;
  font-weight: 600;
  opacity: 1;
  pointer-events: none;
}

/* Auto-save Indicator */
.auto-save-indicator {
  position: absolute;
  bottom: 20px;
  right: 20px;
  background: var(--secondary-color);
  color: white;
  padding: 8px 12px;
  border-radius: 20px;
  font-size: 0.85rem;
  display: flex;
  align-items: center;
  gap: 5px;
  opacity: 0;
  transform: translateY(10px);
  transition: var(--transition);
  z-index: 100;
}

.auto-save-indicator.show {
  opacity: 1;
  transform: translateY(0);
}

/* Tab status indicator */
.tab-status-indicator {
  position: fixed;
  bottom: 20px;
  left: 20px;
  background: rgba(142, 68, 173, 0.9);
  color: white;
  padding: 8px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 6px;
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.3s ease;
  z-index: 1000;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.tab-status-indicator.show {
  opacity: 1;
  transform: translateY(0);
}

.tab-status-indicator i {
  font-size: 11px;
}

.auto-save-indicator i {
  font-size: 0.9rem;
}

/* Word Count Indicator */
.word-count-indicator {
  position: absolute;
  bottom: 10px;
  left: 20px;
  background: var(--primary-color);
  color: white;
  padding: 8px 12px;
  border-radius: 20px;
  font-size: 0.85rem;
  display: flex;
  align-items: center;
  gap: 5px;
  z-index: 100;
}

.word-count-indicator i {
  font-size: 0.9rem;
}

/* Fullscreen Mode for Notes */
.notes-container.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
  border-radius: 0;
  background: var(--bg-color);
}

.notes-container.fullscreen .notes-editor-container {
  height: calc(100vh - 80px);
}

.notes-container.fullscreen .notes-editor {
  min-height: calc(100vh - 140px);
}

/* Dark Theme for Notes */
[data-theme="dark"] .editor-toolbar {
  background: #2c2c2c;
  border-bottom-color: #444;
}

[data-theme="dark"] .toolbar-btn:hover {
  background: var(--primary-color);
}

[data-theme="dark"] .toolbar-select {
  background: #2c2c2c;
  border-color: #444;
  color: var(--text-color);
}

[data-theme="dark"] .toolbar-color {
  border-color: #444;
}

[data-theme="dark"] .notes-editor blockquote {
  background: #2c2c2c;
  border-left-color: var(--primary-color);
}

[data-theme="dark"] .notes-editor table th {
  background: #2c2c2c;
}

[data-theme="dark"] .notes-editor table th,
[data-theme="dark"] .notes-editor table td {
  border-color: #444;
}

[data-theme="dark"] .search-highlight {
  background: #ffc107;
  color: #000;
}

[data-theme="dark"] .search-highlight.current-highlight {
  background: #ff5722;
  color: white;
}

[data-theme="dark"] .toolbar-search {
  background: #2c2c2c;
  border-color: #444;
  color: var(--text-color);
}

/* Responsive Design for Notes */
@media (max-width: 768px) {
  .notes-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .notes-toolbar {
    width: 100%;
    justify-content: flex-start;
  }

  /* Mobile responsive for tabs */
  .notes-tabs-wrapper {
    padding: 0 10px;
  }

  .notes-tab {
    min-width: 100px;
    max-width: 150px;
    padding: 10px 12px;
    font-size: 13px;
  }

  .notes-tab-title {
    font-size: 13px;
  }

  .notes-tab-close {
    font-size: 11px;
    padding: 1px 3px;
  }

  .add-tab-btn {
    padding: 10px 12px;
    min-width: 40px;
    font-size: 13px;
  }

  .tab-counter {
    padding: 10px 12px;
    font-size: 11px;
    min-width: 40px;
  }

  .tab-help-btn {
    padding: 10px 12px;
    min-width: 40px;
    font-size: 13px;
  }

  .tab-help-content {
    width: 95%;
    max-height: 90vh;
  }

  .tab-help-header {
    padding: 15px;
  }

  .tab-help-header h3 {
    font-size: 1.1rem;
  }

  .tab-help-body {
    padding: 20px 15px;
  }

  .tab-status-indicator {
    bottom: 10px;
    left: 10px;
    padding: 6px 10px;
    font-size: 11px;
  }

  .tab-menu-btn {
    padding: 10px 12px;
    min-width: 40px;
    font-size: 13px;
  }

  .tab-quick-menu {
    min-width: 220px;
    left: 10px !important;
  }

  .quick-menu-item {
    padding: 10px 14px;
    font-size: 13px;
  }

  .editor-toolbar {
    padding: 8px 10px;
    gap: 3px;
  }

  .toolbar-group {
    gap: 1px;
  }

  .toolbar-btn {
    padding: 6px 8px;
    min-width: 32px;
    height: 32px;
    font-size: 0.8rem;
  }

  .toolbar-btn#deleteAllImages {
    color: #e67e22;
  }

  .toolbar-btn#deleteAllImages:hover {
    background: #e67e22;
    color: white;
  }

  .toolbar-separator {
    margin: 0 5px;
  }

  .toolbar-select {
    min-width: 70px;
    font-size: 0.8rem;
  }

  .toolbar-color {
    width: 32px;
    height: 32px;
  }

  .toolbar-search {
    min-width: 100px;
    font-size: 0.8rem;
  }

  .notes-editor {
    padding: 15px;
    font-size: 15px;
  }

  .auto-save-indicator {
    bottom: 15px;
    right: 15px;
    font-size: 0.8rem;
    padding: 6px 10px;
  }

  .word-count-indicator {
    bottom: 15px;
    left: 15px;
    font-size: 0.8rem;
    padding: 6px 10px;
  }
}

@media (max-width: 480px) {
  .notes-header h2 {
    font-size: 1.3rem;
  }

  .notes-toolbar .btn {
    padding: 6px 12px;
    font-size: 0.8rem;
  }

  .editor-toolbar {
    padding: 6px 8px;
    flex-wrap: wrap;
  }

  .toolbar-btn {
    padding: 5px 6px;
    min-width: 28px;
    height: 28px;
    font-size: 0.75rem;
  }

  .toolbar-select {
    min-width: 60px;
    font-size: 0.75rem;
    padding: 4px 6px;
  }

  .toolbar-color {
    width: 28px;
    height: 28px;
  }

  .toolbar-search {
    min-width: 80px;
    font-size: 0.75rem;
    padding: 4px 6px;
  }

  .notes-editor {
    padding: 12px;
    font-size: 14px;
    min-height: 400px;
  }

  .auto-save-indicator {
    bottom: 10px;
    right: 10px;
    font-size: 0.75rem;
    padding: 5px 8px;
  }

  .word-count-indicator {
    bottom: 10px;
    left: 10px;
    font-size: 0.75rem;
    padding: 5px 8px;
  }

  .image-modal-content {
    max-width: 95%;
    max-height: 95%;
  }

  .image-modal-close {
    top: 10px;
    right: 15px;
    font-size: 30px;
  }

  .notes-editor img {
    margin: 5px 0;
  }

  .datetime-stamp {
    font-size: 0.8em !important;
    padding: 2px 6px !important;
  }
}

/* Dark mode for notes tabs */
[data-theme="dark"] .notes-tabs-container {
  background: #2d2d2d;
  border-bottom: 1px solid #404040;
}

[data-theme="dark"] .notes-tab {
  background: #3d3d3d;
  color: #e0e0e0;
  border-right: 1px solid #404040;
}

[data-theme="dark"] .notes-tab:hover {
  background: #4d4d4d;
  color: #ffffff;
}

[data-theme="dark"] .notes-tab.active {
  background: #1a1a1a;
  color: #9b59b6;
  border-bottom: 3px solid #9b59b6;
}

[data-theme="dark"] .notes-tab-title {
  color: inherit;
  background: transparent;
}

[data-theme="dark"] .notes-tab-title:focus {
  background: rgba(155, 89, 182, 0.2);
}

[data-theme="dark"] .notes-tab-title:hover {
  background: rgba(155, 89, 182, 0.1);
}

[data-theme="dark"] .notes-tab-close {
  color: #999;
}

[data-theme="dark"] .notes-tab-close:hover {
  background: #dc3545;
  color: white;
}

[data-theme="dark"] .add-tab-btn {
  background: #9b59b6;
  border-left: 1px solid #404040;
}

[data-theme="dark"] .add-tab-btn:hover {
  background: #8e44ad;
}

[data-theme="dark"] .tab-counter {
  background: #2d2d2d;
  border-left: 1px solid #404040;
  color: #999;
}

[data-theme="dark"] #tabCount {
  color: #9b59b6;
}

[data-theme="dark"] .tab-help-btn {
  background: #17a2b8;
  border-left: 1px solid #404040;
}

[data-theme="dark"] .tab-help-btn:hover {
  background: #138496;
}

[data-theme="dark"] .tab-menu-btn {
  background: #6c757d;
  border-left: 1px solid #404040;
}

[data-theme="dark"] .tab-menu-btn:hover {
  background: #5a6268;
}

/* Dark mode for tab context menu */
[data-theme="dark"] .tab-context-menu {
  background: #2d2d2d;
  border-color: #404040;
}

[data-theme="dark"] .context-menu-item {
  color: #e0e0e0;
  border-bottom-color: #404040;
}

[data-theme="dark"] .context-menu-item:hover {
  background: #3d3d3d;
}

[data-theme="dark"] .context-menu-item.danger {
  color: #f44336;
}

[data-theme="dark"] .context-menu-item.danger:hover {
  background: rgba(244, 67, 54, 0.1);
}

[data-theme="dark"] .context-menu-separator {
  background: #404040;
}

/* Dark mode for tab help modal */
[data-theme="dark"] .tab-help-modal {
  background: rgba(0, 0, 0, 0.7);
}

[data-theme="dark"] .tab-help-content {
  background: #2d2d2d;
}

[data-theme="dark"] .tab-help-header {
  background: linear-gradient(135deg, #2c3e50, #34495e);
}

[data-theme="dark"] .tab-help-body {
  color: #e0e0e0;
}

[data-theme="dark"] .tab-help-body h4 {
  color: #ffffff;
}

[data-theme="dark"] .tab-help-body strong {
  color: #9b59b6;
  background: #3d3d3d;
}

[data-theme="dark"] .tab-help-footer {
  border-top-color: #404040;
}

/* Dark mode for tab quick menu */
[data-theme="dark"] .tab-quick-menu {
  background: #2d2d2d;
  border-color: #404040;
}

[data-theme="dark"] .quick-menu-header {
  background: linear-gradient(135deg, #34495e, #2c3e50);
}

[data-theme="dark"] .quick-menu-item {
  color: #e0e0e0;
  border-bottom-color: #404040;
}

[data-theme="dark"] .quick-menu-item:hover {
  background: #3d3d3d;
}

[data-theme="dark"] .quick-menu-item i {
  color: #999;
}

[data-theme="dark"] .shortcut {
  color: #999;
  background: #3d3d3d;
}

[data-theme="dark"] .quick-menu-separator {
  background: #404040;
}

/* Dark mode for tab status indicator */
[data-theme="dark"] .tab-status-indicator {
  background: rgba(155, 89, 182, 0.9);
  border-color: rgba(255, 255, 255, 0.1);
}

/* Dark mode for tab drag and drop */
[data-theme="dark"] .notes-tab.dragging {
  background: #4d4d4d;
  color: #9b59b6;
}

/* Dark mode for tab animations */
[data-theme="dark"] .notes-tab.active::before {
  background: linear-gradient(90deg, #9b59b6, #8e44ad);
}

/* Dark mode for tab tooltips */
[data-theme="dark"] .add-tab-btn::after,
[data-theme="dark"] .notes-tab-close::after {
  background: rgba(0, 0, 0, 0.9);
  color: #e0e0e0;
}

/* Dark mode for mobile responsive tabs */
@media (max-width: 768px) {
  [data-theme="dark"] .notes-tabs-wrapper {
    background: #2d2d2d;
  }

  [data-theme="dark"] .notes-tab {
    background: #3d3d3d;
    border-right-color: #404040;
  }

  [data-theme="dark"] .notes-tab.active {
    background: #1a1a1a;
    color: #9b59b6;
  }
}

/* Balance status styles */
.balance-positive {
    background: linear-gradient(135deg, #2d5a27, #4a7c59) !important;
    color: white !important;
    border: 2px solid #4a7c59 !important;
    box-shadow: 0 4px 15px rgba(74, 124, 89, 0.3) !important;
    transition: all 0.3s ease !important;
}

.balance-negative {
    background: linear-gradient(135deg, #8b1538, #dc3545) !important;
    color: white !important;
    border: 2px solid #dc3545 !important;
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3) !important;
    animation: pulse-danger 2s infinite !important;
    transition: all 0.3s ease !important;
}

/* Balance icon animations */
.balance-icon {
    margin-left: 10px;
    font-size: 1.2em;
    display: inline-block;
}

.balance-icon.positive {
    color: #ffd700;
    animation: coin-spin 2s linear infinite;
}

.balance-icon.negative {
    color: #ff6b6b;
    animation: danger-shake 1s ease-in-out infinite;
}

/* Income Statistics Grid */
.income-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
    padding: 20px 0;
}

.income-stat-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    padding: 25px;
    color: white;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    gap: 20px;
    min-height: 100px;
}

.income-stat-card::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent
    );
    transition: left 0.5s;
}

.income-stat-card:hover::before {
    left: 100%;
}

.income-stat-card:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: 0 15px 40px rgba(102, 126, 234, 0.4);
}

.income-stat-card .stat-icon {
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: #ffffff;
    flex-shrink: 0;
}

.income-stat-card .stat-info {
    flex: 1;
}

.income-stat-card .stat-info h3 {
    margin: 0 0 10px 0;
    font-size: 1.1rem;
    font-weight: 600;
    opacity: 0.9;
}

.income-stat-card .stat-amount {
    margin: 0;
    font-size: 1.8rem;
    font-weight: 700;
    color: #ffffff;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Individual card colors */
.income-stat-card.total-income {
    background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
    box-shadow: 0 8px 25px rgba(46, 204, 113, 0.3);
}

.income-stat-card.monthly-income {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    box-shadow: 0 8px 25px rgba(52, 152, 219, 0.3);
}

.income-stat-card.average-income {
    background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
    box-shadow: 0 8px 25px rgba(243, 156, 18, 0.3);
}

.income-stat-card.last-income {
    background: linear-gradient(135deg, #e67e22 0%, #d35400 100%);
    box-shadow: 0 8px 25px rgba(230, 126, 34, 0.3);
}

/* Expense Statistics Grid */
.expense-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
    padding: 20px 0;
}

.expense-stat-card {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    border-radius: 15px;
    padding: 25px;
    color: white;
    box-shadow: 0 8px 25px rgba(231, 76, 60, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    gap: 20px;
    min-height: 100px;
}

.expense-stat-card::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent
    );
    transition: left 0.5s;
}

.expense-stat-card:hover::before {
    left: 100%;
}

.expense-stat-card:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: 0 15px 40px rgba(231, 76, 60, 0.4);
}

.expense-stat-card .stat-icon {
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: #ffffff;
    flex-shrink: 0;
}

.expense-stat-card .stat-info {
    flex: 1;
}

.expense-stat-card .stat-info h3 {
    margin: 0 0 10px 0;
    font-size: 1.1rem;
    font-weight: 600;
    opacity: 0.9;
}

.expense-stat-card .stat-amount {
    margin: 0;
    font-size: 1.8rem;
    font-weight: 700;
    color: #ffffff;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Individual expense card colors */
.expense-stat-card.total-expense {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    box-shadow: 0 8px 25px rgba(231, 76, 60, 0.3);
}

.expense-stat-card.monthly-expense {
    background: linear-gradient(135deg, #e67e22 0%, #d35400 100%);
    box-shadow: 0 8px 25px rgba(230, 126, 34, 0.3);
}

.expense-stat-card.average-expense {
    background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
    box-shadow: 0 8px 25px rgba(243, 156, 18, 0.3);
}

.expense-stat-card.last-expense {
    background: linear-gradient(135deg, #e67e22 0%, #d35400 100%);
    box-shadow: 0 8px 25px rgba(230, 126, 34, 0.3);
}

/* Loan Statistics Grid */
.loan-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
    padding: 20px 0;
}

.loan-stat-card {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    border-radius: 15px;
    padding: 25px;
    color: white;
    box-shadow: 0 8px 25px rgba(52, 152, 219, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    gap: 20px;
    min-height: 100px;
}

.loan-stat-card::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent
    );
    transition: left 0.5s;
}

.loan-stat-card:hover::before {
    left: 100%;
}

.loan-stat-card:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: 0 15px 40px rgba(52, 152, 219, 0.4);
}

.loan-stat-card .stat-icon {
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: #ffffff;
    flex-shrink: 0;
}

.loan-stat-card .stat-info {
    flex: 1;
}

.loan-stat-card .stat-info h3 {
    margin: 0 0 10px 0;
    font-size: 1.1rem;
    font-weight: 600;
    opacity: 0.9;
}

.loan-stat-card .stat-amount {
    margin: 0;
    font-size: 1.8rem;
    font-weight: 700;
    color: #ffffff;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Individual loan card colors */
.loan-stat-card.total-loan-taken {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    box-shadow: 0 8px 25px rgba(231, 76, 60, 0.3);
}

.loan-stat-card.total-loan-paid {
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
    box-shadow: 0 8px 25px rgba(39, 174, 96, 0.3);
}

.loan-stat-card.total-loan-given {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    box-shadow: 0 8px 25px rgba(52, 152, 219, 0.3);
}

.loan-stat-card.total-loan-returned {
    background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
    box-shadow: 0 8px 25px rgba(155, 89, 182, 0.3);
}

/* Bank Statistics Grid */
.bank-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
    padding: 20px 0;
}

.bank-stat-card {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    border-radius: 15px;
    padding: 10px 25px;
    color: white;
    box-shadow: 0 8px 25px rgba(52, 152, 219, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    gap: 20px;
    min-height: 100px;
}

.bank-stat-card::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent
    );
    transition: left 0.5s;
}

.bank-stat-card:hover::before {
    left: 100%;
}

.bank-stat-card:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: 0 15px 40px rgba(52, 152, 219, 0.4);
}

.bank-stat-card .stat-icon {
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: #ffffff;
    flex-shrink: 0;
}

.bank-stat-card .stat-info {
    flex: 1;
}

.bank-stat-card .stat-info h3 {
    margin: 0 0 10px 0;
    font-size: 1.1rem;
    font-weight: 600;
    opacity: 0.9;
}

.bank-stat-card .stat-amount {
    margin: 0;
    font-size: 1.8rem;
    font-weight: 700;
    color: #ffffff;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Individual bank card colors */
.bank-stat-card.current-balance {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.bank-stat-card.last-transaction {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    box-shadow: 0 8px 25px rgba(240, 147, 251, 0.3);
}

.bank-stat-card.total-deposit {
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
    box-shadow: 0 8px 25px rgba(39, 174, 96, 0.3);
}

.bank-stat-card.total-withdraw {
    background: linear-gradient(135deg, #e67e22 0%, #d35400 100%);
    box-shadow: 0 8px 25px rgba(230, 126, 34, 0.3);
}

/* Keyframe animations */
@keyframes pulse-danger {
    0% {
        box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
    }
    50% {
        box-shadow: 0 6px 20px rgba(220, 53, 69, 0.6);
    }
    100% {
        box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
    }
}

@keyframes coin-spin {
    0% {
        transform: rotateY(0deg);
    }
    50% {
        transform: rotateY(180deg);
    }
    100% {
        transform: rotateY(360deg);
    }
}

@keyframes danger-shake {
    0%, 100% {
        transform: translateX(0);
    }
    10%, 30%, 50%, 70%, 90% {
        transform: translateX(-2px);
    }
    20%, 40%, 60%, 80% {
        transform: translateX(2px);
    }
}

/* Notification Alert Messages */
.notification-alert {
    position: fixed;
    top: 20px;
    left: -400px;
    width: 350px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
    border-right: 5px solid #3498db;
    z-index: 10001;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.notification-alert.show {
    left: 20px;
    animation: slideInFromLeft 0.5s ease-out;
}

.notification-alert.hide {
    left: -400px;
    animation: slideOutToLeft 0.3s ease-in;
}

.notification-alert-header {
    padding: 15px 20px;
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 600;
    font-size: 16px;
}

.notification-alert-header i {
    font-size: 18px;
    animation: pulse 2s infinite;
}

.notification-alert-body {
    padding: 20px;
    color: #2c3e50;
    font-size: 15px;
    line-height: 1.5;
    font-family: var(--bengali-font);
}

.notification-alert-actions {
    padding: 15px 20px;
    background: #f8f9fa;
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    border-top: 1px solid #e9ecef;
}

.notification-alert-btn {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    font-family: var(--bengali-font);
}

.notification-alert-btn.primary {
    background: #3498db;
    color: white;
}

.notification-alert-btn.primary:hover {
    background: #2980b9;
    transform: translateY(-1px);
}

.notification-alert-btn.secondary {
    background: #95a5a6;
    color: white;
}

.notification-alert-btn.secondary:hover {
    background: #7f8c8d;
    transform: translateY(-1px);
}

/* Different alert types */
.notification-alert.success {
    border-right-color: #27ae60;
}

.notification-alert.success .notification-alert-header {
    background: linear-gradient(135deg, #27ae60, #2ecc71);
}

.notification-alert.warning {
    border-right-color: #f39c12;
}

.notification-alert.warning .notification-alert-header {
    background: linear-gradient(135deg, #f39c12, #e67e22);
}

.notification-alert.error {
    border-right-color: #e74c3c;
}

.notification-alert.error .notification-alert-header {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
}

.notification-alert.info {
    border-right-color: #3498db;
}

.notification-alert.info .notification-alert-header {
    background: linear-gradient(135deg, #3498db, #2980b9);
}

/* Notification alert animations */
@keyframes slideInFromLeft {
    0% {
        transform: translateX(-100%);
        opacity: 0;
    }
    100% {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutToLeft {
    0% {
        transform: translateX(0);
        opacity: 1;
    }
    100% {
        transform: translateX(-100%);
        opacity: 0;
    }
}

/* Progress bar for auto-dismiss */
.notification-alert-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 3px;
    background: rgba(255, 255, 255, 0.3);
    transition: width linear;
}

.notification-alert.success .notification-alert-progress {
    background: #27ae60;
}

.notification-alert.warning .notification-alert-progress {
    background: #f39c12;
}

.notification-alert.error .notification-alert-progress {
    background: #e74c3c;
}

.notification-alert.info .notification-alert-progress {
    background: #3498db;
}

/* Notification Panel Animations */
@keyframes slideInPanelFromLeft {
    0% {
        transform: translateX(-100%);
        opacity: 0;
        box-shadow: 8px 0 32px rgba(0, 0, 0, 0);
    }
    50% {
        opacity: 0.8;
        box-shadow: 8px 0 32px rgba(0, 0, 0, 0.1);
    }
    100% {
        transform: translateX(0);
        opacity: 1;
        box-shadow: 8px 0 32px rgba(0, 0, 0, 0.15);
    }
}

@keyframes slideOutPanelToLeft {
    0% {
        transform: translateX(0);
        opacity: 1;
        box-shadow: 8px 0 32px rgba(0, 0, 0, 0.15);
    }
    50% {
        opacity: 0.5;
        box-shadow: 8px 0 32px rgba(0, 0, 0, 0.05);
    }
    100% {
        transform: translateX(-100%);
        opacity: 0;
        box-shadow: 8px 0 32px rgba(0, 0, 0, 0);
    }
}

/* Enhanced notification panel items animation */
.notification-item {
    transform: translateX(-20px);
    opacity: 0;
    transition: all 0.3s ease;
}

.notification-panel.show .notification-item {
    transform: translateX(0);
    opacity: 1;
}

.notification-panel.show .notification-item:nth-child(1) {
    transition-delay: 0.1s;
}

.notification-panel.show .notification-item:nth-child(2) {
    transition-delay: 0.15s;
}

.notification-panel.show .notification-item:nth-child(3) {
    transition-delay: 0.2s;
}

.notification-panel.show .notification-item:nth-child(4) {
    transition-delay: 0.25s;
}

.notification-panel.show .notification-item:nth-child(5) {
    transition-delay: 0.3s;
}

/* Notification Detail Modal Styles */
.notification-detail-modal-content {
    max-width: 600px;
    width: 90%;
    max-height: 88vh;
    overflow-y: auto;
    background: white;
    border-radius: 12px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease-out;
    margin-left: auto;
    margin-right: auto;
    margin-top: auto;
    margin-bottom: auto;
}

@keyframes modalSlideIn {
    from {
        transform: scale(0.7);
        opacity: 0;
    }
    to {
        transform: scale(1);
        opacity: 1;
    }
}

.notification-detail-content {
    padding: 20px;
}

.detail-grid {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.detail-card {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 12px;
    border-left: 4px solid #3498db;
    margin-bottom: 20px;
}

.detail-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
}

.detail-icon.income {
    background: linear-gradient(135deg, #27ae60, #2ecc71);
}

.detail-icon.expense {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
}

.detail-icon.loan-given {
    background: linear-gradient(135deg, #f39c12, #e67e22);
}

.detail-icon.loan-taken {
    background: linear-gradient(135deg, #9b59b6, #8e44ad);
}

.detail-info h3 {
    margin: 0;
    font-size: 1.2rem;
    color: #2c3e50;
    font-weight: 600;
}

.detail-info p {
    margin: 5px 0 0 0;
    color: #7f8c8d;
    font-size: 0.9rem;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: white;
    border-radius: 8px;
    border: 1px solid #ecf0f1;
    transition: all 0.3s ease;
}

.detail-row:hover {
    background: #f8f9fa;
    border-color: #3498db;
    transform: translateX(5px);
}

.detail-label {
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 600;
    color: #2c3e50;
    min-width: 150px;
}

.detail-label i {
    color: #3498db;
    width: 16px;
}

.detail-value {
    color: #34495e;
    text-align: right;
    flex: 1;
    margin-left: 10px;
    font-weight: 500;
}

.detail-value.amount {
    color: #27ae60;
    font-weight: 700;
    font-size: 1.1rem;
}

.detail-value.category {
    background: #3498db;
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.9rem;
}

.detail-value.status {
    padding: 6px 12px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.9rem;
}

.detail-value.status-paid {
    background: #d5f4e6;
    color: #27ae60;
}

.detail-value.status-pending {
    background: #fef9e7;
    color: #f39c12;
}

.detail-value.id {
    font-family: 'Courier New', monospace;
    background: #ecf0f1;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
}

.notification-detail-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    flex-wrap: wrap;
    padding: 20px;
    background: #f8f9fa;
    border-top: 1px solid #ecf0f1;
}

.notification-detail-actions .btn {
    padding: 12px 20px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
    font-size: 0.9rem;
}

.notification-detail-actions .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.notification-detail-actions .btn-primary {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
}

.notification-detail-actions .btn-success {
    background: linear-gradient(135deg, #27ae60, #229954);
    color: white;
}

.notification-detail-actions .btn-warning {
    background: linear-gradient(135deg, #f39c12, #e67e22);
    color: white;
}

.notification-detail-actions .btn-danger {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
}

.notification-detail-actions .btn-secondary {
    background: linear-gradient(135deg, #95a5a6, #7f8c8d);
    color: white;
}

/* Dark theme for notification detail modal */
[data-theme="dark"] .notification-detail-modal-content {
    background: #2c2c2c;
    color: #ecf0f1;
}

[data-theme="dark"] .detail-card {
    background: linear-gradient(135deg, #34495e, #2c3e50);
    border-left-color: #3498db;
}

[data-theme="dark"] .detail-info h3 {
    color: #ecf0f1;
}

[data-theme="dark"] .detail-info p {
    color: #bdc3c7;
}

[data-theme="dark"] .detail-row {
    background: #34495e;
    border-color: #555;
}

[data-theme="dark"] .detail-row:hover {
    background: #3c5a78;
    border-color: #3498db;
}

[data-theme="dark"] .detail-label {
    color: #bdc3c7;
}

[data-theme="dark"] .detail-value {
    color: #f39c12;
}

[data-theme="dark"] .detail-value.id {
    background: #555;
    color: #ecf0f1;
}

[data-theme="dark"] .notification-detail-actions {
    background: #34495e;
    border-top-color: #555;
}

/* Dark theme for modal header */
[data-theme="dark"] .modal-header {
    background: linear-gradient(135deg, #34495e, #2c3e50);
    color: #ecf0f1;
}

[data-theme="dark"] .modal-header h2 {
    color: #ecf0f1;
}

/* Responsive design for notification detail modal */
@media (max-width: 768px) {
    .notification-detail-modal-content {
        width: 95%;
        margin: 10px;
    }

    .detail-card {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }

    .detail-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .detail-label {
        min-width: auto;
    }

    .detail-value {
        text-align: left;
        margin-left: 0;
    }

    .notification-detail-actions {
        flex-direction: column;
    }

    .notification-detail-actions .btn {
        width: 100%;
        justify-content: center;
    }
}

/* Notification Analytics Styles */
.notification-analytics-card .feature-header {
  background: linear-gradient(135deg, #3498db, #2980b9);
}

.notification-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-bottom: 25px;
}

.notification-stat {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  background: var(--light-color);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
}

.notification-stat .stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  color: white;
}

.notification-stat .stat-icon.total {
  background: linear-gradient(135deg, #3498db, #2980b9);
}

.notification-stat .stat-icon.unread {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
}

.notification-stat .stat-icon.today {
  background: linear-gradient(135deg, #2ecc71, #27ae60);
}

.notification-stat .stat-info h4 {
  margin: 0 0 5px 0;
  font-size: 0.9rem;
  color: var(--text-color);
  opacity: 0.8;
}

.notification-stat .stat-count {
  font-size: 1.5rem;
  font-weight: bold;
  color: var(--primary-color);
}

.notification-chart-container {
  margin-bottom: 25px;
}

.notification-chart-container .chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.notification-chart-container .chart-header h4 {
  margin: 0;
  font-size: 1rem;
  color: var(--text-color);
}

.notification-type-chart h4 {
  margin: 0 0 15px 0;
  font-size: 1rem;
  color: var(--text-color);
}

.notification-chart-container .chart-container,
.notification-type-chart .chart-container {
  height: 200px;
  position: relative;
}

/* Responsive Bottom Grid */
@media (max-width: 768px) {
  .bottom-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .notification-stats,
  .reminder-stats {
    grid-template-columns: 1fr;
  }

  .notification-chart-container .chart-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
}

/* Header glow animation */
@keyframes headerGlow {
  0%, 100% {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15),
                0 0 30px rgba(52, 152, 219, 0.3);
  }
  50% {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15),
                0 0 40px rgba(52, 152, 219, 0.5);
  }
}

/* Enhanced Report Styles */
.report-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 25px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 15px;
  color: white;
  animation: slideInDown 0.6s ease;
}

.report-title h2 {
  margin: 0;
  font-size: 28px;
  font-weight: 700;
}

.report-title p {
  margin: 5px 0 0 0;
  opacity: 0.9;
  font-size: 16px;
}

.report-quick-actions {
  display: flex;
  gap: 10px;
}

.report-quick-actions .btn {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.report-quick-actions .btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

/* Report Filters Card */
.report-filters-card {
  background: white;
  border-radius: 15px;
  box-shadow: 0 8px 25px rgba(0,0,0,0.1);
  margin-bottom: 30px;
  overflow: hidden;
  animation: fadeInUp 0.6s ease;
}

.report-filters-card .card-header {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.report-filters-card .card-header h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.filter-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  padding: 25px;
}

.filter-group {
  display: flex;
  flex-direction: column;
}

.filter-group label {
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 8px;
  font-size: 14px;
}

.filter-group .form-control {
  padding: 12px 15px;
  border: 2px solid #e1e8ed;
  border-radius: 10px;
  font-size: 14px;
  transition: all 0.3s ease;
  background: #f8f9fa;
}

.filter-group .form-control:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  background: white;
  outline: none;
}

.filter-actions {
  padding: 0 25px 25px 25px;
  display: flex;
  gap: 15px;
  justify-content: center;
}

/* Report Summary Grid */
.report-summary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
  animation: fadeIn 0.8s ease;
}

.summary-card {
  background: white;
  border-radius: 15px;
  padding: 25px;
  box-shadow: 0 8px 25px rgba(0,0,0,0.1);
  border-left: 5px solid;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.summary-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.summary-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 35px rgba(0,0,0,0.15);
}

.summary-card.income {
  border-left-color: #27ae60;
}

.summary-card.expense {
  border-left-color: #e74c3c;
}

.summary-card.bank {
  border-left-color: #3498db;
}

.summary-card.loan {
  border-left-color: #f39c12;
}

.summary-card.balance {
  border-left-color: #9b59b6;
}

.summary-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.summary-card-title {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

.summary-card-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
}

.summary-card.income .summary-card-icon {
  background: linear-gradient(135deg, #27ae60, #2ecc71);
}

.summary-card.expense .summary-card-icon {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
}

.summary-card.bank .summary-card-icon {
  background: linear-gradient(135deg, #3498db, #2980b9);
}

.summary-card.loan .summary-card-icon {
  background: linear-gradient(135deg, #f39c12, #e67e22);
}

.summary-card.balance .summary-card-icon {
  background: linear-gradient(135deg, #9b59b6, #8e44ad);
}

.summary-card-amount {
  font-size: 28px;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 10px;
}

.summary-card-details {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
  color: #7f8c8d;
}

/* Charts Section */
.report-charts-section {
  margin-bottom: 30px;
}

.charts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 25px;
}

.chart-card {
  background: white;
  border-radius: 15px;
  box-shadow: 0 8px 25px rgba(0,0,0,0.1);
  overflow: hidden;
  animation: fadeInUp 0.8s ease;
}

.chart-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-header h4 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.chart-controls .btn {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.chart-controls .btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.05);
}

.chart-container {
  padding: 25px;
  height: 350px;
  position: relative;
}

.chart-container canvas {
  max-width: 100%;
  max-height: 100%;
}

/* Report Tables Section */
.report-tables-section {
  background: white;
  border-radius: 15px;
  box-shadow: 0 8px 25px rgba(0,0,0,0.1);
  overflow: hidden;
  margin-bottom: 30px;
  animation: fadeInUp 1s ease;
}

.table-tabs {
  display: flex;
  background: #f8f9fa;
  border-bottom: 1px solid #e1e8ed;
}

.table-tab {
  flex: 1;
  padding: 15px 20px;
  background: transparent;
  border: none;
  font-weight: 600;
  color: #6c757d;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.table-tab:hover {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
}

.table-tab.active {
  background: white;
  color: #667eea;
  border-bottom: 3px solid #667eea;
}

.table-tab.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #667eea, #764ba2);
}

.table-content {
  position: relative;
}

.table-container {
  display: none;
  padding: 25px;
  max-height: 500px;
  overflow-y: auto;
}

.table-container.active {
  display: block;
}

.report-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 20px;
}

.report-table th {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  color: #495057;
  font-weight: 600;
  padding: 12px 15px;
  text-align: left;
  border-bottom: 2px solid #dee2e6;
  position: sticky;
  top: 0;
  z-index: 10;
}

.report-table td {
  padding: 12px 15px;
  border-bottom: 1px solid #e9ecef;
  transition: background-color 0.2s ease;
}

.report-table tr:hover td {
  background-color: #f8f9fa;
}

.report-table .amount {
  font-weight: 600;
  text-align: right;
}

.report-table .amount.positive {
  color: #27ae60;
}

.report-table .amount.negative {
  color: #e74c3c;
}

/* Export Section */
.report-export-section {
  background: white;
  border-radius: 15px;
  box-shadow: 0 8px 25px rgba(0,0,0,0.1);
  padding: 25px;
  animation: fadeInUp 1.2s ease;
}

.export-card h4 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  font-size: 20px;
  font-weight: 600;
}

.export-buttons {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.export-buttons .btn {
  padding: 15px 20px;
  font-weight: 600;
  border-radius: 10px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.export-buttons .btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(0,0,0,0.15);
}

/* Custom Date Range */
.custom-date-range {
  animation: slideDown 0.3s ease;
}

/* Dark Mode Support for Reports */
[data-theme="dark"] .report-header {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
}

[data-theme="dark"] .report-filters-card {
  background: #2C2C2C;
  border: 1px solid var(--dark-border-color);
}

[data-theme="dark"] .report-filters-card .card-header {
  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
}

[data-theme="dark"] .filter-group .form-control {
  background: #2C2C2C;
  border-color: var(--dark-border-color);
  color: var(--dark-text-color);
}

[data-theme="dark"] .filter-group .form-control:focus {
  background: #2C2C2C;
  border-color: #667eea;
  color: var(--dark-text-color);
}

[data-theme="dark"] .filter-group label {
  color: var(--dark-text-color);
}

[data-theme="dark"] .summary-card {
  background: #2C2C2C;
  border: 1px solid var(--dark-border-color);
}

[data-theme="dark"] .summary-card-title,
[data-theme="dark"] .summary-card-amount {
  color: var(--dark-text-color);
}

[data-theme="dark"] .summary-card-details {
  color: var(--dark-text-muted);
}

[data-theme="dark"] .chart-card {
  background: #2C2C2C;
  border: 1px solid var(--dark-border-color);
}

[data-theme="dark"] .chart-header {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
}

[data-theme="dark"] .report-tables-section {
  background: #2C2C2C;
  border: 1px solid var(--dark-border-color);
}

[data-theme="dark"] .table-tabs {
  background: #2C2C2C;
  border-bottom-color: var(--dark-border-color);
}

[data-theme="dark"] .table-tab {
  color: var(--dark-text-muted);
}

[data-theme="dark"] .table-tab:hover {
  background: rgba(102, 126, 234, 0.2);
  color: #667eea;
}

[data-theme="dark"] .table-tab.active {
  background: var(--dark-bg-color);
  color: #667eea;
}

[data-theme="dark"] .report-table th {
  background: var(--dark-input-bg);
  color: var(--dark-text-color);
  border-color: var(--dark-border-color);
}

[data-theme="dark"] .report-table td {
  border-color: var(--dark-border-color);
  color: var(--dark-text-color);
}

[data-theme="dark"] .report-table tr:hover td {
  background-color: var(--dark-input-bg);
}

[data-theme="dark"] .report-export-section {
  background: var(--dark-bg-color);
  border: 1px solid var(--dark-border-color);
}

[data-theme="dark"] .export-card h4 {
  color: var(--dark-text-color);
}

[data-theme="dark"] .badge.bg-success {
  background-color: #27ae60 !important;
}

[data-theme="dark"] .badge.bg-warning {
  background-color: #f39c12 !important;
}

[data-theme="dark"] .badge.bg-info {
  background-color: #3498db !important;
}

[data-theme="dark"] .badge.bg-danger {
  background-color: #e74c3c !important;
}

/* Responsive Design for Reports */
@media (max-width: 768px) {
  .report-header {
    flex-direction: column;
    text-align: center;
    gap: 15px;
  }

  .filter-grid {
    grid-template-columns: 1fr;
  }

  .report-summary-grid {
    grid-template-columns: 1fr;
  }

  .charts-grid {
    grid-template-columns: 1fr;
  }

  .chart-container {
    height: 300px;
    padding: 15px;
  }

  .table-tabs {
    flex-wrap: wrap;
  }

  .table-tab {
    flex: none;
    min-width: 120px;
  }

  .export-buttons {
    grid-template-columns: 1fr;
  }
}
